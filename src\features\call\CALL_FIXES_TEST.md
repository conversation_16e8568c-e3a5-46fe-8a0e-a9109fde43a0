# Call System Fixes - Test Guide

## ❌ **Vấn đề đã được sửa:**

### **1. Accept Call - Timer không start**
**Vấn đề**: <PERSON>hi accept incoming call, CallScreen hiển thị "<PERSON>uộ<PERSON> gọi bắt đầu" nhưng timer không chạy.

**Nguyên nhân**: CallScreen chỉ start timer khi `state.isConnected = true`, nhưng incoming call khi accept chưa connected ngay lập tức.

**Fix**: 
- Start timer ngay khi incoming call được accept (`!state.isIncoming && state.isInCall`)
- Update `callStartTime` trong WebRTC khi accept call
- Thêm debug logs để track state changes

### **2. Reject Call - Người gọi không quay lại**
**Vấn đề**: Khi reject call, người gọi vẫn stuck trong CallScreen.

**Nguyên nhân**: `onCallRejected` callback chỉ show <PERSON><PERSON> nhưng không cleanup call state.

**Fix**:
- Call `endCall()` trong Alert callback để cleanup và navigate back
- Clear timeout trong WebRTC khi nhận reject-call event
- Đảm bảo cleanup() được gọi ngay lập tức

## ✅ **Fixes Applied:**

### **Fix 1: CallScreen Timer Logic**
```typescript
// Trước (có vấn đề)
if (state.isConnected && !callTimerRef.current) {
  startCallTimer();
}

// Sau (đã fix)
if (!callTimerRef.current) {
  if (isIncoming && !state.isIncoming && state.isInCall) {
    // Incoming call accepted - start timer ngay
    startCallTimer();
  } else if (!isIncoming && state.isConnected) {
    // Outgoing call connected - start timer
    startCallTimer();
  }
}
```

### **Fix 2: WebRTC Accept Call State**
```typescript
// Thêm callStartTime khi accept
this.updateCallState({
  isIncoming: false,
  isConnected: true,
  callStartTime: new Date(), // NEW: Update start time
});
```

### **Fix 3: Reject Call Cleanup**
```typescript
// CallScreen - cleanup khi reject
onCallRejected: () => {
  Alert.alert('Cuộc gọi bị từ chối', '', [
    { text: 'OK', onPress: () => {
      endCall(); // NEW: Cleanup và navigate back
    }}
  ]);
},

// WebRTC - clear timeout khi reject
socket.on('reject-call', (data) => {
  this.clearCallTimeout(); // NEW: Clear timeout
  if (this.callbacks.onCallRejected) {
    this.callbacks.onCallRejected(data);
  }
  this.cleanup(); // Cleanup ngay lập tức
});
```

### **Fix 4: Enhanced Debug Logging**
```typescript
private updateCallState(updates: Partial<CallState>) {
  const oldState = { ...this.callState };
  this.callState = { ...this.callState, ...updates };
  
  console.log('📞 WebRTC State Update:', {
    from: oldState,
    to: this.callState,
    updates
  });
  
  if (this.callbacks.onCallStateChanged) {
    this.callbacks.onCallStateChanged(this.callState);
  }
}
```

## 🧪 **Test Scenarios:**

### **Test 1: Accept Incoming Call Timer**
1. **Setup**: User A calls User B
2. **Action**: User B accepts call via FloatingIncomingCall
3. **Expected**: 
   - ✅ Navigate to CallScreen
   - ✅ Timer starts immediately (00:01, 00:02, ...)
   - ✅ Status shows "Đang gọi" or "Kết nối"
   - ✅ No "Cuộc gọi bắt đầu" stuck state

### **Test 2: Reject Incoming Call Cleanup**
1. **Setup**: User A calls User B
2. **Action**: User B rejects call via FloatingIncomingCall
3. **Expected**:
   - ✅ User A sees "Cuộc gọi bị từ chối" alert
   - ✅ User A taps OK → navigates back to previous screen
   - ✅ User A not stuck in CallScreen
   - ✅ Call state properly cleaned up

### **Test 3: Outgoing Call (Regression Test)**
1. **Setup**: User A calls User B
2. **Action**: User B accepts call
3. **Expected**:
   - ✅ User A timer starts when connected
   - ✅ Both users can see call duration
   - ✅ Call works normally

### **Test 4: Call End Cleanup**
1. **Setup**: Any active call
2. **Action**: Either user ends call
3. **Expected**:
   - ✅ Both users navigate back
   - ✅ Timer stops
   - ✅ Resources cleaned up
   - ✅ Ready for next call

## 📱 **Console Logs to Watch:**

### **Accept Call Flow:**
```
📞 User tapped Accept button, callData: {...}
📞 Accepting call from: John Doe
📞 WebRTC State Update: {
  from: { isIncoming: true, isConnected: false },
  to: { isIncoming: false, isConnected: true },
  updates: { isIncoming: false, isConnected: true, callStartTime: ... }
}
📞 CallScreen received state change: { isIncoming: false, isConnected: true, ... }
📞 Incoming call accepted, starting timer
```

### **Reject Call Flow:**
```
📞 User tapped Reject button, callData: {...}
📞 Rejecting call from: John Doe
📞 Call rejected by: user-123
📞 WebRTC State Update: {
  from: { isInCall: true },
  to: { isInCall: false },
  updates: { isInCall: false, ... }
}
📞 CallScreen received state change: { isInCall: false }
```

## 🎯 **Success Criteria:**

### **✅ Accept Call:**
- Timer starts immediately when accept
- CallScreen shows proper status
- Call duration counts up normally
- No stuck "Cuộc gọi bắt đầu" state

### **✅ Reject Call:**
- Caller receives reject notification
- Caller navigates back to previous screen
- No stuck CallScreen state
- Resources properly cleaned up

### **✅ General:**
- Console logs show proper state transitions
- No memory leaks or stuck timers
- Ready for subsequent calls
- Both incoming and outgoing calls work

## 🚨 **If Issues Persist:**

### **Debug Steps:**
1. **Check console logs** for state transitions
2. **Verify WebRTC callbacks** are properly set
3. **Test socket events** are being received
4. **Check timer refs** are not null/undefined

### **Common Issues:**
- **Timer not starting**: Check `isIncoming` flag and state transitions
- **Stuck in CallScreen**: Verify `endCall()` is called and navigation works
- **State not updating**: Check WebRTC callbacks and socket events

### **Manual Reset:**
```typescript
// If stuck, manually reset WebRTC state
WebRTCService.endCall();
// Or force navigation
navigation.goBack();
```

## 🎉 **Expected Results:**

After these fixes:
- ✅ **Accept call timer works** immediately
- ✅ **Reject call cleanup works** properly  
- ✅ **No stuck states** in CallScreen
- ✅ **Proper navigation** back to previous screens
- ✅ **Clean state management** for subsequent calls

**Test these scenarios to verify the fixes work correctly!** 🎯

# Accept Call Fixes - State Management

## 🐛 **Problem Identified:**
Debug shows `isIncoming = true, isConnected = false` after accept call.

## 🔧 **Root Cause:**
1. **Race condition**: CallScreen mounts với `isIncoming: true` từ navigation params
2. **WebRTC state update**: Happens sau khi CallScreen đã mount
3. **State override**: Navigation params override WebRTC state

## ✅ **Fixes Applied:**

### **Fix 1: Change Navigation Flow**
```typescript
// IncomingCallProvider - Accept call BEFORE navigate
const handleAcceptCall = async (data: CallData) => {
  // Accept call through WebRTC TRƯỚC khi navigate
  await WebRTCService.acceptCall();
  
  // Navigate với isIncoming: false vì đã accept
  navigate(RootScreen.CallScreen, {
    isIncoming: false, // Changed from true to false
    callerId: data.callerId,
    callerName: data.callerName,
    callerAvatar: data.callerAvatar,
  });
};
```

### **Fix 2: CallScreen Initial State**
```typescript
// CallScreen - Get current WebRTC state on mount
useEffect(() => {
  // Lấy current state từ WebRTC khi mount
  const currentWebRTCState = WebRTCService.getCallState();
  console.log('📞 CallScreen mounted, current WebRTC state:', currentWebRTCState);
  setCallState(currentWebRTCState);
  
  // Then setup callbacks...
}, []);
```

### **Fix 3: Enhanced Timer Conditions**
```typescript
// Multiple conditions để start timer
if (!callTimerRef.current && state.isInCall && state.isConnected) {
  // Primary condition
  startCallTimer();
} else if (!callTimerRef.current && state.isInCall && !state.isIncoming) {
  // Backup condition - call active và không phải incoming
  startCallTimer();
}
```

### **Fix 4: Manual Timer Test**
```typescript
// Test timer sau 3 giây nếu vẫn chưa start
useEffect(() => {
  const testTimer = setTimeout(() => {
    if (!callTimerRef.current && callState.isInCall) {
      console.log('📞 Manual starting timer');
      startCallTimer();
    }
  }, 3000);
  return () => clearTimeout(testTimer);
}, [callState]);
```

### **Fix 5: Enhanced WebRTC Logging**
```typescript
// WebRTC acceptCall với detailed logs
console.log('📞 WebRTC: Updating state after accept call');
this.updateCallState({
  isIncoming: false,
  isConnected: true,
  callStartTime: new Date(),
});
console.log('📞 WebRTC: State updated, new state:', this.callState);
```

## 📱 **Expected Flow Now:**

### **Step 1: Accept Call**
```
📞 User taps Accept in FloatingIncomingCall
📞 Calling WebRTCService.acceptCall() first
📞 WebRTC: Updating state after accept call
📞 WebRTC State Update: { isIncoming: false, isConnected: true }
📞 WebRTC acceptCall completed
📞 Navigate to CallScreen with isIncoming: false
```

### **Step 2: CallScreen Mount**
```
📞 CallScreen mounted, current WebRTC state: { isIncoming: false, isConnected: true }
📞 CallScreen received state change: { isIncoming: false, isConnected: true }
📞 Timer conditions: { hasTimer: false, isInCall: true, isConnected: true, isIncoming: false }
📞 ✅ Starting timer - all conditions met
📞 startCallTimer() called
📞 Timer tick: 1, 2, 3...
```

### **Step 3: UI Display**
```
Debug: isIncoming=false, isConnected=true
Controls: Mic, Speaker, End Call buttons (NOT Accept/Reject)
Timer: 00:01, 00:02, 00:03...
```

## 🧪 **Test Steps:**

### **1. Trigger Incoming Call**
```typescript
IncomingCallOverlayService.showIncomingCall({
  callerName: 'Test User',
  callerId: 'test-123',
});
```

### **2. Accept Call**
- Tap Accept in FloatingIncomingCall
- Watch console logs carefully
- Check CallScreen debug text

### **3. Expected Results**
- ✅ Debug text: `isIncoming=false, isConnected=true`
- ✅ Controls: Mic, Speaker, End Call (NOT Accept/Reject)
- ✅ Timer: Starts immediately và counts up
- ✅ Console: Shows proper state transitions

## 🚨 **If Still Not Working:**

### **Debug Checklist:**
1. **WebRTC State**: Check if `acceptCall()` actually updates state
2. **Navigation Timing**: Verify accept happens before navigate
3. **CallScreen Mount**: Check initial state from WebRTC
4. **Timer Conditions**: Verify all conditions are met

### **Manual Fixes:**
```typescript
// Force state update in CallScreen
useEffect(() => {
  setTimeout(() => {
    setCallState({
      isInCall: true,
      isIncoming: false,
      isConnected: true,
      isOutgoing: false,
    });
  }, 1000);
}, []);

// Force timer start
useEffect(() => {
  setTimeout(() => {
    if (!callTimerRef.current) {
      startCallTimer();
    }
  }, 2000);
}, []);
```

### **Simplify Timer Condition:**
```typescript
// Most basic condition
if (!callTimerRef.current && state.isInCall) {
  startCallTimer();
}
```

## 🎯 **Success Criteria:**

### **✅ State Management:**
- WebRTC state updates before navigation
- CallScreen gets correct initial state
- No race conditions between navigation và state updates

### **✅ UI Display:**
- Debug text shows `isIncoming=false, isConnected=true`
- Shows proper controls (mic/speaker/end)
- Timer starts và counts up immediately

### **✅ Console Logs:**
- Shows accept call → state update → navigation flow
- Timer conditions met và timer starts
- No errors or stuck states

## 🔄 **Key Changes Summary:**

1. **Accept call BEFORE navigate** - eliminates race condition
2. **Navigate với isIncoming: false** - correct initial state
3. **Get WebRTC state on mount** - sync với actual state
4. **Multiple timer triggers** - backup mechanisms
5. **Enhanced debugging** - track every step

**Test với these fixes và check debug text trên CallScreen!** 🎯

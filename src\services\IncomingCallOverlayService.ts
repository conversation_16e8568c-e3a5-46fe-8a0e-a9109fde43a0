// Service để quản lý incoming call notification state
// Sử dụng event emitter pattern để communicate với UI components

import { EventEmitter } from 'events';

interface CallData {
  callerName: string;
  callerAvatar?: string;
  callerId: string;
}

class IncomingCallOverlayService extends EventEmitter {
  private static instance: IncomingCallOverlayService;
  private isShowing = false;
  private currentCallData: CallData | null = null;

  private constructor() {
    super();
  }

  static getInstance(): IncomingCallOverlayService {
    if (!IncomingCallOverlayService.instance) {
      IncomingCallOverlayService.instance = new IncomingCallOverlayService();
    }
    return IncomingCallOverlayService.instance;
  }

  // Hiển thị incoming call notification
  showIncomingCall(callData: CallData): void {
    if (this.isShowing) {
      console.log('Incoming call notification already showing');
      return;
    }

    this.isShowing = true;
    this.currentCallData = callData;
    console.log('📞 Showing incoming call notification for:', callData.callerName);

    // Emit event để UI components có thể listen và hiển thị
    this.emit('showIncomingCall', callData);
  }

  // Ẩn incoming call notification
  hideIncomingCall(): void {
    if (!this.isShowing) {
      return;
    }

    this.isShowing = false;
    this.currentCallData = null;
    console.log('📞 Hiding incoming call notification');

    // Emit event để UI components ẩn notification
    this.emit('hideIncomingCall');
  }

  // Accept cuộc gọi
  acceptCall(): void {
    if (this.currentCallData) {
      console.log('📞 Accepting call from:', this.currentCallData.callerName);
      this.emit('acceptCall', this.currentCallData);
      this.hideIncomingCall();
    }
  }

  // Reject cuộc gọi
  rejectCall(): void {
    if (this.currentCallData) {
      console.log('📞 Rejecting call from:', this.currentCallData.callerName);
      this.emit('rejectCall', this.currentCallData);
      this.hideIncomingCall();
    }
  }

  // Kiểm tra xem có đang hiển thị notification không
  isShowingNotification(): boolean {
    return this.isShowing;
  }

  // Lấy thông tin cuộc gọi hiện tại
  getCurrentCallData(): CallData | null {
    return this.currentCallData;
  }
}

export default IncomingCallOverlayService.getInstance();

import React, {useEffect, useState, useCallback} from 'react';
import {View, ActivityIndicator, Text, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {ProductDA} from './productDA';
import {RootScreen} from '../../router/router';
import {ColorThemes} from '../../assets/skin/colors';
import HotProductsRow from './HotProductsRow';
import {getImage} from '../../redux/actions/rootAction';

interface HotProductsSectionProps {
  title?: string;
  pageSize?: number;
  onSeeAll?: () => void;
  onRefresh?: boolean;
}

const HotProductsSection: React.FC<HotProductsSectionProps> = ({
  title = 'Sản phẩm HOT',
  pageSize = 10,
  onSeeAll,
  onRefresh = false,
}) => {
  const navigation = useNavigation<any>();
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (onRefresh) {
      fetchHotProducts();
    }
  }, [onRefresh]);

  // Hàm lấy dữ liệu sản phẩm HOT
  const fetchHotProducts = useCallback(async () => {
    const productDA = new ProductDA();

    setLoading(true);
    setError(null);

    try {
      const response = await productDA.getProductHot(1, pageSize);
      if (response && response.code === 200 && response.data) {
        const data = await getImage({items: response.data});
        // Chuyển đổi dữ liệu từ API sang định dạng SquareProductItem
        const formattedProducts: any[] = data.map((item: any) => ({
          Id: item.Id,
          Name: item.Name,
          Price: item.Price,
          Img: item.Img,
          rating: item.rating ?? 0, // Giá trị mặc định hoặc từ API nếu có
          soldCount: item.Sold ?? 0, // Giá trị mặc định hoặc từ API nếu có
          Discount: item.Discount,
        }));

        setProducts(formattedProducts);
      } else {
        setError('Không thể tải dữ liệu sản phẩm');
      }
    } catch (err) {
      console.error('Error fetching hot products:', err);
      setError('Đã xảy ra lỗi khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  }, [pageSize]);

  // Gọi API khi component được mount
  useEffect(() => {
    fetchHotProducts();
  }, [fetchHotProducts]);

  // Xử lý khi nhấn vào sản phẩm
  const handleProductPress = (product: any) => {
    navigation.navigate(RootScreen.ProductDetail, {id: product.Id});
  };

  // Xử lý khi nhấn vào nút "Xem thêm"
  const handleSeeAll = () => {
    if (onSeeAll) {
      onSeeAll();
    } else {
      navigation.navigate(RootScreen.HotProductsDemo);
    }
  };

  // Hiển thị loading indicator khi đang tải dữ liệu
  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  // Hiển thị thông báo nếu không có sản phẩm
  if (products.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.noProductText}>Không có sản phẩm HOT</Text>
      </View>
    );
  }

  // Hiển thị danh sách sản phẩm HOT
  return (
    <HotProductsRow
      title={title}
      products={products}
      onSeeAll={handleSeeAll}
      onProductPress={handleProductPress}
      showRating={true}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
  },
  errorText: {
    color: ColorThemes.light.error_main_color,
  },
  noProductText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default HotProductsSection;

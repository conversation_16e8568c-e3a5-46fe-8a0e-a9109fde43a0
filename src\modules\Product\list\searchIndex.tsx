import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {AppSvg, FBottomSheet, FDialog, TextField} from 'wini-mobile-components';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {ColorThemes} from '../../../assets/skin/colors';
import {RootScreen} from '../../../router/router';
import {useNavigation, useRoute} from '@react-navigation/native';
import EmptyPage from '../../../Screen/emptyPage';

import ProductCard from '../card/ProductCard';
import {CartActions} from '../../../redux/reducers/CartReducer';
import {AppDispatch} from '../../../redux/store/store';
import {useDispatch} from 'react-redux';
import HeaderLogo from '../../../Screen/Layout/headers/HeaderLogo';
import iconSvg from '../../../svg/icon';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import {DataController} from '../../../base/baseController';
import {DefaultWithImage} from '../../news/card/defaultImage';
import {TypoSkin} from '../../../assets/skin/typography';

const {width} = Dimensions.get('window');

const ITEM_WIDTH = width * 0.45;
const ITEM_HEIGHT = ITEM_WIDTH * 2; // Tăng chiều cao một chút

export const SearchIndex = () => {
  const [searchValue, setSearchValue] = useState('');
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const [data, setData] = useState<Array<any>>([]);
  const [productData, setProductData] = useState<Array<any>>([]);
  const [newsData, setNewsData] = useState<Array<any>>([]);
  const [isRefresh, setRefresh] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const route = useRoute<any>();
  const type = route?.params?.type;
  const navigation = useNavigation<any>();

  const productDA = new DataController('Product');
  const newController = new DataController('News');
  const eventController = new DataController('NewsEvent');
  const dispatch: AppDispatch = useDispatch();
  // Add this ref for the TextField
  const textInputRef = useRef<any>(null);
  // Search history and recent data based on type
  const [searchHistory, setSearchHistory] = useState<any>([]);
  const [recentData, setRecentData] = useState<any>([]);
  const [recentProducts, setRecentProducts] = useState<any>([]);
  const [recentNews, setRecentNews] = useState<any>([]);

  // Dynamic keys based on search type
  const SEARCH_HISTORY_KEY = `search_history_${type || 'product&news'}`;
  const RECENT_DATA_KEY = `recent_data_${type || 'product&news'}`;
  const RECENT_PRODUCTS_KEY = `recent_products_${type || 'product&news'}`;
  const RECENT_NEWS_KEY = `recent_news_${type || 'product&news'}`;
  const MAX_HISTORY_ITEMS = 5;
  const MAX_RECENT_DATA = 6;

  // Load search history and recent data when component mounts
  useEffect(() => {
    loadSearchHistory();
    loadRecentData();
    loadRecentProducts();
    loadRecentNews();
  }, []);

  const loadSearchHistory = async () => {
    try {
      const history = await getDataToAsyncStorage(SEARCH_HISTORY_KEY);
      if (history !== null) {
        setSearchHistory(JSON.parse(history));
      }
    } catch (error) {
      console.error('Error loading search history:', error);
    }
  };

  const loadRecentData = async () => {
    try {
      const data = await getDataToAsyncStorage(RECENT_DATA_KEY);
      if (data !== null) {
        setRecentData(JSON.parse(data));
      }
    } catch (error) {
      console.error('Error loading recent data:', error);
    }
  };

  const loadRecentProducts = async () => {
    try {
      const data = await getDataToAsyncStorage(RECENT_PRODUCTS_KEY);
      if (data !== null) {
        setRecentProducts(JSON.parse(data));
      }
    } catch (error) {
      console.error('Error loading recent products:', error);
    }
  };

  const loadRecentNews = async () => {
    try {
      const data = await getDataToAsyncStorage(RECENT_NEWS_KEY);
      if (data !== null) {
        setRecentNews(JSON.parse(data));
      }
    } catch (error) {
      console.error('Error loading recent news:', error);
    }
  };

  const saveSearchHistory = async (newHistory: any) => {
    try {
      await saveDataToAsyncStorage(
        SEARCH_HISTORY_KEY,
        JSON.stringify(newHistory),
      );
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  };

  const saveRecentData = async (newData: any) => {
    try {
      await saveDataToAsyncStorage(RECENT_DATA_KEY, JSON.stringify(newData));
    } catch (error) {
      console.error('Error saving recent data:', error);
    }
  };

  const saveRecentProducts = async (newData: any) => {
    try {
      await saveDataToAsyncStorage(
        RECENT_PRODUCTS_KEY,
        JSON.stringify(newData),
      );
    } catch (error) {
      console.error('Error saving recent products:', error);
    }
  };

  const saveRecentNews = async (newData: any) => {
    try {
      await saveDataToAsyncStorage(RECENT_NEWS_KEY, JSON.stringify(newData));
    } catch (error) {
      console.error('Error saving recent news:', error);
    }
  };

  const addToSearchHistory = async (searchTerm: string) => {
    const trimmedTerm = searchTerm.trim();

    if (trimmedTerm && !searchHistory.includes(trimmedTerm)) {
      let newHistory = [trimmedTerm, ...searchHistory];

      // Limit to MAX_HISTORY_ITEMS
      if (newHistory.length > MAX_HISTORY_ITEMS) {
        newHistory = newHistory.slice(0, MAX_HISTORY_ITEMS);
      }

      setSearchHistory(newHistory);
      await saveSearchHistory(newHistory);
    }
  };

  const addToRecentData = async (searchResults: any[]) => {
    if (searchResults && searchResults.length > 0) {
      // Take first few items from search results
      const dataToAdd = searchResults.slice(0, 3);

      // Remove duplicates and add new data to the beginning
      let newRecentData = [...dataToAdd];

      // Add existing data that are not duplicates
      recentData.forEach((existingItem: any) => {
        const isDuplicate = newRecentData.some(
          (newItem: any) => newItem.Id === existingItem.Id,
        );
        if (!isDuplicate && newRecentData.length < MAX_RECENT_DATA) {
          newRecentData.push(existingItem);
        }
      });

      // Limit to MAX_RECENT_DATA
      if (newRecentData.length > MAX_RECENT_DATA) {
        newRecentData = newRecentData.slice(0, MAX_RECENT_DATA);
      }

      setRecentData(newRecentData);
      await saveRecentData(newRecentData);
    }
  };

  const addToRecentProducts = async (productResults: any[]) => {
    if (productResults && productResults.length > 0) {
      // Take first few items from product results
      const productsToAdd = productResults.slice(0, 3);

      // Remove duplicates and add new products to the beginning
      let newRecentProducts = [...productsToAdd];

      // Add existing products that are not duplicates
      recentProducts.forEach((existingProduct: any) => {
        const isDuplicate = newRecentProducts.some(
          (newProduct: any) => newProduct.Id === existingProduct.Id,
        );
        if (!isDuplicate && newRecentProducts.length < MAX_RECENT_DATA) {
          newRecentProducts.push(existingProduct);
        }
      });

      // Limit to MAX_RECENT_DATA
      if (newRecentProducts.length > MAX_RECENT_DATA) {
        newRecentProducts = newRecentProducts.slice(0, MAX_RECENT_DATA);
      }

      setRecentProducts(newRecentProducts);
      await saveRecentProducts(newRecentProducts);
    }
  };

  const addToRecentNews = async (newsResults: any[]) => {
    if (newsResults && newsResults.length > 0) {
      // Take first few items from news results
      const newsToAdd = newsResults.slice(0, 3);

      // Remove duplicates and add new news to the beginning
      let newRecentNews = [...newsToAdd];

      // Add existing news that are not duplicates
      recentNews.forEach((existingNews: any) => {
        const isDuplicate = newRecentNews.some(
          (newNewsItem: any) => newNewsItem.Id === existingNews.Id,
        );
        if (!isDuplicate && newRecentNews.length < MAX_RECENT_DATA) {
          newRecentNews.push(existingNews);
        }
      });

      // Limit to MAX_RECENT_DATA
      if (newRecentNews.length > MAX_RECENT_DATA) {
        newRecentNews = newRecentNews.slice(0, MAX_RECENT_DATA);
      }

      setRecentNews(newRecentNews);
      await saveRecentNews(newRecentNews);
    }
  };

  const handleHistoryItemPress = async (term: string) => {
    setSearchValue(term);
    // Trigger search immediately
    await performSearch(term);
  };

  // New unified search function that handles different types
  const performSearch = useCallback(
    async (
      searchText: string,
      pageNumber: number = 1,
      isRefresh: boolean = false,
    ) => {
      if (!searchText.trim()) {
        setData([]);
        setProductData([]);
        setNewsData([]);
        return;
      }

      try {
        if (pageNumber === 1) {
          isRefresh ? setRefresh(true) : setIsLoading(true);
        }

        let allResults: any[] = [];
        let productResults: any[] = [];
        let newsResults: any[] = [];

        // Handle different search types
        if (type === 'product&news') {
          // Call all 3 APIs: productDA, newController, eventController
          const [productResult, newsResult, eventResult] = await Promise.all([
            productDA.getListSimple({
              page: pageNumber,
              size: 7,
              query: `@Name: (*${searchText.trim()}*)`,
            }),
            newController.getListSimple({
              page: pageNumber,
              size: 7,
              query: `@Title: (*${searchText.trim()}*) | @Content: (*${searchText.trim()}*)`,
            }),
            eventController.getListSimple({
              page: pageNumber,
              size: 6,
              query: `@Title: (*${searchText.trim()}*) | @Content: (*${searchText.trim()}*)`,
            }),
          ]);

          // Separate results by type
          if (productResult?.code === 200) {
            productResults = [...(productResult.data || [])];
            allResults = [...allResults, ...productResults];
          }
          if (newsResult?.code === 200) {
            const newsItems = newsResult.data || [];
            newsResults = [...newsResults, ...newsItems];
            allResults = [...allResults, ...newsItems];
          }
          if (eventResult?.code === 200) {
            const eventItems = eventResult.data || [];
            newsResults = [...newsResults, ...eventItems];
            allResults = [...allResults, ...eventItems];
          }
        } else if (type === 'news') {
          // Call only 2 APIs: newController, eventController
          const [newsResult, eventResult] = await Promise.all([
            newController.getListSimple({
              page: pageNumber,
              size: 10,
              query: `@Title: (*${searchText.trim()}*) | @Content: (*${searchText.trim()}*)`,
            }),
            eventController.getListSimple({
              page: pageNumber,
              size: 10,
              query: `@Title: (*${searchText.trim()}*) | @Content: (*${searchText.trim()}*)`,
            }),
          ]);

          // Combine results (all news for news-only search)
          if (newsResult?.code === 200) {
            const newsItems = newsResult.data || [];
            newsResults = [...newsResults, ...newsItems];
            allResults = [...allResults, ...newsItems];
          }
          if (eventResult?.code === 200) {
            const eventItems = eventResult.data || [];
            newsResults = [...newsResults, ...eventItems];
            allResults = [...allResults, ...eventItems];
          }
        } else {
          // Default to product search only
          const result = await productDA.getListSimple({
            page: pageNumber,
            size: 20,
            query: `@Name: (*${searchText.trim()}*)`,
          });

          if (result?.code === 200) {
            productResults = result.data || [];
            allResults = productResults;
          }
        }

        if (pageNumber === 1) {
          setData(allResults);
          setProductData(productResults);
          setNewsData(newsResults);
          // Add to search history and recent data only when search is completed successfully
          if (allResults.length > 0) {
            await addToSearchHistory(searchText);
            await addToRecentData(allResults);

            // Add to separate recent lists
            if (productResults.length > 0) {
              await addToRecentProducts(productResults);
            }
            if (newsResults.length > 0) {
              await addToRecentNews(newsResults);
            }
          }
        } else {
          setData(prev => [...prev, ...allResults]);
          setProductData(prev => [...prev, ...productResults]);
          setNewsData(prev => [...prev, ...newsResults]);
        }

        setRefresh(false);
      } catch (err) {
        console.error('Error performing search:', err);
        if (pageNumber === 1) {
          setData([]);
          setProductData([]);
          setNewsData([]);
        }
      } finally {
        setIsLoading(false);
        setRefresh(false);
      }
    },
    [productDA, newController, eventController, type],
  );

  const onRefresh = async () => {
    await performSearch(searchValue);
  };

  return (
    <View
      style={{
        width: '100%',
        height: Dimensions.get('window').height,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      <HeaderLogo isShowBack />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          flex: 1,
          paddingHorizontal: 16,
          gap: 8,
          marginBottom: 32,
          zIndex: 999,
        }}>
        <TextField
          ref={textInputRef}
          style={{
            paddingHorizontal: 16,
            width: '90%',
            height: 36,
          }}
          onChange={async (vl: string) => {
            setSearchValue(vl.trim());
            await performSearch(vl.trim());
          }}
          value={searchValue}
          placeholder="Bạn muốn tìm gì?"
        />
        <AppSvg
          SvgSrc={iconSvg.filter}
          color="#000"
          size={25}
          style={{marginRight: 12}}
        />
      </View>
      <ScrollView
        style={{
          width: '100%',
          height: '100%',
        }}
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        contentContainerStyle={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          paddingHorizontal: 16,
          paddingTop: 16,
        }}>
        {/* Loading State */}
        {isLoading && (
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
              paddingVertical: 50,
            }}>
            <ActivityIndicator color={ColorThemes.light.Primary_Color_Main} />
          </View>
        )}

        {/* Empty State - No search results */}
        {!isLoading && searchValue.trim().length > 0 && data.length === 0 && (
          <EmptyPage
            title="Không tìm thấy nội dung"
            subtitle={`Không có kết quả nào cho "${searchValue}"`}
          />
        )}

        {/* Empty State - No search term */}
        {!isLoading && searchValue.trim().length === 0 && (
          <EmptySearchWithHistory
            histories={searchHistory}
            recentData={recentData}
            recentProducts={recentProducts}
            recentNews={recentNews}
            onHistoryPress={handleHistoryItemPress}
            onItemPress={(item: any) => {
              // Handle different item types based on search type
              if (item.Name && type === 'product&news') {
                // Product item
                navigation.push(RootScreen.ProductDetail, {id: item.Id});
              } else if (item.Title) {
                // News or Event item
                navigation.navigate(RootScreen.DetailNews, {id: item.Id});
              }
            }}
            onAddToCart={(product: any) => {
              // Only add to cart if it's a product
              if (product.Name) {
                dispatch(CartActions.addItemToCart(product, 1));
              }
            }}
            searchType={type}
          />
        )}

        {/* Products Section */}
        {!isLoading && productData.length > 0 && (
          <View style={{marginBottom: 24}}>
            <Text style={styles.sectionTitle}>Sản phẩm</Text>
            <FlatList
              data={productData}
              scrollEnabled={false}
              numColumns={2}
              keyExtractor={item => `product-${item.Id?.toString()}`}
              contentContainerStyle={{gap: 16}}
              renderItem={({item, index}) => (
                <ProductCard
                  key={index}
                  onPress={() => {
                    navigation.push(RootScreen.ProductDetail, {id: item.Id});
                  }}
                  item={item}
                  onAddToCart={() => {
                    dispatch(CartActions.addItemToCart(item, 1));
                  }}
                  onFavoritePress={() => {}}
                  width={ITEM_WIDTH}
                  height={ITEM_HEIGHT}
                />
              )}
            />
          </View>
        )}

        {/* News Section */}
        {!isLoading && newsData.length > 0 && (
          <View style={{marginBottom: 24}}>
            <Text style={styles.sectionTitle}>Tin tức & Sự kiện</Text>
            <FlatList
              data={newsData}
              scrollEnabled={false}
              numColumns={1}
              keyExtractor={item => `news-${item.Id?.toString()}`}
              contentContainerStyle={{gap: 16}}
              renderItem={({item}) => (
                <DefaultWithImage
                  onPressDetail={() => {
                    navigation.navigate(RootScreen.DetailNews, {id: item.Id});
                  }}
                  data={item}
                  containerStyle={{width: width - 32, marginBottom: 16}}
                  imgStyle={{width: width - 32, height: 180}}
                  subtitleView={
                    <View
                      style={{
                        paddingTop: 4,
                        width: '100%',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.subtitle3,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                          width: '80%',
                        }}>
                        {item.Address ?? ''}
                      </Text>
                    </View>
                  }
                />
              )}
            />
          </View>
        )}
      </ScrollView>
    </View>
  );
};

interface EmptySearchWithHistoryProps {
  histories: string[];
  recentData: any[];
  recentProducts: any[];
  recentNews: any[];
  onHistoryPress: (term: string) => void;
  onItemPress: (item: any) => void;
  onAddToCart: (product: any) => void;
  searchType?: string;
}

const EmptySearchWithHistory = ({
  histories,
  recentData,
  recentProducts,
  recentNews,
  onHistoryPress,
  onItemPress,
  onAddToCart,
  searchType,
}: EmptySearchWithHistoryProps) => {
  return (
    <View style={{height: '100%', width: '100%'}}>
      {/* History Section */}
      {histories && histories.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Lịch sử tìm kiếm</Text>
          <View style={styles.categoryContainer}>
            {histories.map((term: string, index: number) => (
              <TouchableOpacity
                key={index}
                style={styles.categoryButton}
                onPress={() => onHistoryPress(term)}>
                <Text style={styles.categoryText}>{term}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Recent Products Section */}
      {recentProducts && recentProducts.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sản phẩm gần đây</Text>
          <View style={styles.productGrid}>
            {recentProducts.map((item: any) => (
              <ProductCard
                key={item.Id}
                onPress={() => onItemPress(item)}
                onAddToCart={() => onAddToCart(item)}
                item={item}
                onFavoritePress={() => {}}
                width={ITEM_WIDTH}
                height={ITEM_HEIGHT}
              />
            ))}
          </View>
        </View>
      )}

      {/* Recent News Section */}
      {recentNews && recentNews.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tin tức gần đây</Text>
          <View style={{flexDirection: 'column'}}>
            {recentNews.map((item: any) => (
              <DefaultWithImage
                key={item.Id}
                onPressDetail={() => onItemPress(item)}
                data={item}
                containerStyle={{width: width - 32, marginBottom: 16}}
                imgStyle={{width: width - 32, height: 180}}
                subtitleView={
                  <View
                    style={{
                      paddingTop: 4,
                      width: '100%',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.subtitle3,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                        width: '80%',
                      }}>
                      {item.Address ?? ''}
                    </Text>
                  </View>
                }
              />
            ))}
          </View>
        </View>
      )}

      {/* Fallback: Show combined recent data if separate lists are empty but combined exists */}
      {(!recentProducts || recentProducts.length === 0) &&
        (!recentNews || recentNews.length === 0) &&
        recentData &&
        recentData.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {searchType === 'news' ? 'Tin tức gần đây' : 'Sản phẩm gần đây'}
            </Text>
            <View
              style={
                searchType === 'news'
                  ? {flexDirection: 'column'}
                  : styles.productGrid
              }>
              {recentData.map((item: any) => {
                // Check if it's a product (has Name property)
                if (searchType === 'product&news' && item.Name) {
                  return (
                    <ProductCard
                      key={item.Id}
                      onPress={() => onItemPress(item)}
                      onAddToCart={() => onAddToCart(item)}
                      item={item}
                      onFavoritePress={() => {}}
                      width={ITEM_WIDTH}
                      height={ITEM_HEIGHT}
                    />
                  );
                } else {
                  // For news/events, create a simple card
                  return (
                    <DefaultWithImage
                      key={item.Id}
                      onPressDetail={() => onItemPress(item)}
                      data={item}
                      containerStyle={{width: width - 32, marginBottom: 16}}
                      imgStyle={{width: width - 32, height: 180}}
                      subtitleView={
                        <View
                          style={{
                            paddingTop: 4,
                            width: '100%',
                            flexDirection: 'row',
                            alignItems: 'center',
                          }}>
                          <Text
                            style={{
                              ...TypoSkin.subtitle3,
                              color:
                                ColorThemes.light.neutral_text_subtitle_color,
                              width: '80%',
                            }}>
                            {item.Address ?? ''}
                          </Text>
                        </View>
                      }
                    />
                  );
                }
              })}
            </View>
          </View>
        )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#4A90E2',
    paddingTop: 10,
    paddingBottom: 15,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  menuIcon: {
    width: 24,
    height: 24,
    justifyContent: 'space-between',
  },
  menuLine: {
    width: 20,
    height: 2,
    backgroundColor: 'white',
  },
  logo: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    letterSpacing: 1,
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 15,
  },
  gameIcon: {
    width: 24,
    height: 24,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 12,
  },
  profileIcon: {
    width: 24,
    height: 24,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 12,
  },
  cartIcon: {
    width: 24,
    height: 24,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 12,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#FF6B6B',
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 8,
    minWidth: 16,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 12,
    fontSize: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  filterButton: {
    marginLeft: 10,
    backgroundColor: 'white',
    borderRadius: 25,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  filterIcon: {
    fontSize: 18,
    color: '#666',
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  categoryButton: {
    backgroundColor: '#E8E8E8',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    marginBottom: 10,
  },
  categoryText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  productGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  productCard: {
    width: '48%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 10,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 8,
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#E0E0E0',
    borderRadius: 8,
  },
  productTitle: {
    fontSize: 12,
    color: '#333',
    marginBottom: 5,
    lineHeight: 16,
  },
  productPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4A90E2',
  },
});

import {useState, useEffect, useCallback, useMemo} from 'react';
import {useDispatch} from 'react-redux';
import {ProductDA} from '../../productDA';
import {CustomerDA} from '../../../customer/da';
import {ProductData, ShopData} from '../types';
import {DataController} from '../../../../base/baseController';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {StatusOrder, TypeMenuPorduct} from '../../../../Config/Contanst';
import {useSelectorCustomerState} from '../../../../redux/hook/customerHook';
import {AppDispatch} from '../../../../redux/store/store';

interface UseProductDetailResult {
  loading: boolean;
  refreshing: boolean;
  data: ProductData | null;
  shop: ShopData | null;
  productImages: string[];
  like: boolean;
  setLike: (value: boolean) => void;
  getData: () => Promise<void>;
  onRefresh: () => void;
  setLoading: (value: boolean) => void;
}

export const useProductDetail = (productId: string): UseProductDetailResult => {
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [data, setData] = useState<ProductData | null>(null);
  const [shop, setShop] = useState<ShopData | null>(null);
  const [productImages, setProductImages] = useState<string[]>([]);
  const [like, setLike] = useState<boolean>(false);

  const dispatch: AppDispatch = useDispatch();
  const customer = useSelectorCustomerState().data;

  // Data controllers - memoized to prevent recreation
  const customerDA = useMemo(() => new CustomerDA(), []);
  const productDA = useMemo(() => new ProductDA(), []);
  const productFavorite = useMemo(
    () => new DataController('ProductFavorite'),
    [],
  );
  const ratingController = useMemo(() => new DataController('Rating'), []);
  const productController = useMemo(() => new DataController('Product'), []);
  const orderController = useMemo(() => new DataController('Order'), []);

  const getData = useCallback(async (): Promise<void> => {
    try {
      if (!productId) {
        setLoading(false);
        setRefreshing(false);
        return;
      }

      const result = await productDA.getProductDetail(productId);
      if (!result) {
        setLoading(false);
        setRefreshing(false);
        return;
      }

      const product = result.data[0];
      const shopData = result.Shop[0];

      // Get shop owner information
      const customerShop = await customerDA.getCustomerItem(
        shopData.CustomerId,
      );

      // Get shop rating
      const ratingShop = await ratingController.group({
        reducers:
          'LOAD * GROUPBY 1 @ShopId REDUCE SUM 1 @Value AS TotalRate REDUCE COUNT 0 AS CountRate',
        searchRaw: `@ShopId: {${shopData.Id}} @Status: [1]`,
      });

      if (ratingShop.code === 200) {
        const totalRate = parseFloat(ratingShop.data[0]?.TotalRate || '0');
        const countRate = parseFloat(ratingShop.data[0]?.CountRate || '0');
        shopData.rating = countRate > 0 ? totalRate / countRate : 0;
      }

      // Get total products of shop
      const totalProducts = await productController.group({
        reducers: 'LOAD * GROUPBY 1 @ShopId REDUCE COUNT 0 AS CountProduct',
        searchRaw: `@ShopId: {${shopData.Id}}`,
      });

      if (totalProducts.code === 200) {
        shopData.totalProducts = totalProducts.data[0]?.CountProduct || 0;
      }

      // Get total orders of shop
      const totalOrder = await orderController.group({
        reducers: 'LOAD * GROUPBY 1 @ShopId REDUCE COUNT 0 AS CountOrder',
        searchRaw: `@ShopId: {${shopData.Id}} @Status: [${StatusOrder.success}]`,
      });

      if (totalOrder.code === 200) {
        shopData.totalOrder = totalOrder.data[0]?.CountOrder || 0;
      }

      // Get related products from same shop and category
      const listProduct = await productController.getListSimple({
        page: 1,
        size: 10,
        query: `@ShopId: {${shopData.Id}} @Status: [${TypeMenuPorduct.InStock.id}] @CategoryId: {${product.CategoryId}}`,
      });

      if (listProduct.code === 200) {
        shopData.products = listProduct.data?.filter(
          (item: any) => item?.Id !== productId,
        );
      }

      // Prepare shop information
      const shopInfo: ShopData = {
        ...shopData,
        Img: customerShop?.AvatarUrl
          ? `${ConfigAPI.urlImg}${customerShop.AvatarUrl}`
          : '',
        OwnerName: customerShop?.Name,
      };

      setShop(shopInfo);

      // Process product images
      if (product?.ListImg || product?.Img) {
        let listImg = product?.ListImg
          ? product.ListImg.split(',')
          : [product.Img];
        listImg = listImg.filter((item: string) => item !== '');
        listImg = listImg.map((item: string) => `${ConfigAPI.urlImg}${item}`);
        setProductImages(listImg);
      }

      // Add brand and category names
      if (product?.BrandId && result?.Brand?.[0]) {
        product.BrandName = result.Brand[0].Name;
      }
      if (product?.CategoryId && result?.Category?.[0]) {
        product.CategoryName = result.Category[0].Name;
      }

      // Get product rating
      const ratingResult = await ratingController.group({
        reducers:
          'LOAD * GROUPBY 1 @ProductId REDUCE SUM 1 @Value AS TotalRate REDUCE COUNT 0 AS CountRate',
        searchRaw: `@ProductId: {${product.Id}}`,
      });

      if (ratingResult.code === 200) {
        const totalRate = parseFloat(ratingResult.data[0]?.TotalRate || '0');
        const countRate = parseFloat(ratingResult.data[0]?.CountRate || '0');
        product.rating = countRate > 0 ? totalRate / countRate : 0;
      }

      // Check if product is favorite
      if (customer?.Id) {
        const favoriteResult = await productFavorite.getListSimple({
          page: 1,
          size: 1,
          query: `@ProductId:{${product.Id}} @CustomerId:{${customer.Id}}`,
        });

        if (favoriteResult.code === 200) {
          product.IsFavorite = favoriteResult.data.length > 0;
          setLike(favoriteResult.data.length > 0);
        }
      }

      setData(product);
    } catch (error) {
      console.error('Error fetching product data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [
    productId,
    productDA,
    customerDA,
    ratingController,
    productController,
    orderController,
    productFavorite,
    customer?.Id,
  ]);

  useEffect(() => {
    getData();
  }, [getData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getData();
  }, [getData]);

  return {
    loading,
    refreshing,
    data,
    shop,
    productImages,
    like,
    setLike,
    getData,
    onRefresh,
    setLoading,
  };
};

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import FastImage from 'react-native-fast-image';

interface CallRecord {
  id: string;
  name: string;
  avatar?: string;
  callType: 'incoming' | 'outgoing' | 'missed';
  duration?: string;
  time: string;
  date: Date;
}

const CallHistoryScreen: React.FC = () => {
  const [callHistory, setCallHistory] = useState<CallRecord[]>([]);

  useEffect(() => {
    // Mock data cho lịch sử cuộc gọi
    const mockCallHistory: CallRecord[] = [
      {
        id: '1',
        name: '<PERSON>ù<PERSON>',
        avatar: 'https://i.pravatar.cc/150?img=1',
        callType: 'outgoing',
        duration: '60 giây',
        time: '1:49 PM',
        date: new Date(),
      },
      {
        id: '2',
        name: '<PERSON><PERSON><PERSON>',
        avatar: 'https://i.pravatar.cc/150?img=2',
        callType: 'incoming',
        duration: '1 phút 60 giây',
        time: '1:49 PM',
        date: new Date(),
      },
      {
        id: '3',
        name: 'Tùng Nguyễn',
        avatar: 'https://i.pravatar.cc/150?img=3',
        callType: 'outgoing',
        duration: '2 phút',
        time: '1:49 PM',
        date: new Date(),
      },
      {
        id: '4',
        name: 'Tùng Nguyễn',
        avatar: 'https://i.pravatar.cc/150?img=4',
        callType: 'missed',
        time: '1:49 PM',
        date: new Date(),
      },
      {
        id: '5',
        name: 'Tùng Nguyễn',
        avatar: 'https://i.pravatar.cc/150?img=5',
        callType: 'incoming',
        duration: '60 phút',
        time: '1:49 PM',
        date: new Date(),
      },
    ];
    setCallHistory(mockCallHistory);
  }, []);

  const getCallIcon = (callType: string) => {
    switch (callType) {
      case 'incoming':
        return '📞'; // Incoming call icon
      case 'outgoing':
        return '📞'; // Outgoing call icon
      case 'missed':
        return '📞'; // Missed call icon
      default:
        return '📞';
    }
  };

  const getCallIconColor = (callType: string) => {
    switch (callType) {
      case 'incoming':
        return '#4CAF50'; // Green for incoming
      case 'outgoing':
        return '#2196F3'; // Blue for outgoing
      case 'missed':
        return '#F44336'; // Red for missed
      default:
        return ColorThemes.light.neutral_text_secondary_color;
    }
  };

  const renderCallItem = ({ item }: { item: CallRecord }) => (
    <TouchableOpacity style={styles.callItem} activeOpacity={0.7}>
      <View style={styles.avatarContainer}>
        {item.avatar ? (
          <FastImage source={{ uri: item.avatar }} style={styles.avatar} />
        ) : (
          <View style={[styles.avatar, styles.defaultAvatar]}>
            <Text style={styles.avatarText}>
              {item.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.callInfo}>
        <Text style={styles.callerName}>{item.name}</Text>
        <Text style={styles.callDuration}>
          {item.duration || 'Cuộc gọi nhỡ'}
        </Text>
      </View>

      <View style={styles.callDetails}>
        <Text style={styles.callTime}>{item.time}</Text>
        <TouchableOpacity 
          style={[styles.callButton, { backgroundColor: getCallIconColor(item.callType) }]}
          activeOpacity={0.7}
        >
          <Text style={styles.callButtonIcon}>{getCallIcon(item.callType)}</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>Chưa có lịch sử cuộc gọi</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={callHistory}
        renderItem={renderCallItem}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={callHistory.length === 0 ? styles.emptyList : undefined}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  callItem: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  callInfo: {
    flex: 1,
  },
  callerName: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
    marginBottom: 4,
  },
  callDuration: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  callDetails: {
    alignItems: 'flex-end',
  },
  callTime: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_secondary_color,
    marginBottom: 8,
  },
  callButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  callButtonIcon: {
    fontSize: 16,
    color: 'white',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
  },
});

export default CallHistoryScreen;

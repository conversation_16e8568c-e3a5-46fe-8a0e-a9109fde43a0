import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  Alert,
  BackHandler,
} from 'react-native';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { ColorThemes } from '../../../assets/skin/colors';
import { Winicon } from 'wini-mobile-components';
import WebRTCService, { CallState } from '../../../features/call/WebRTCService';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../../Config/ConfigAPI';

const { width, height } = Dimensions.get('window');

interface CallScreenParams {
  contact?: {
    Id: string;
    Name: string;
    AvatarUrl?: string;
  };
  isIncoming?: boolean;
  callerId?: string;
  callerName?: string;
  callerAvatar?: string;
}

type CallScreenRouteProp = RouteProp<{ CallScreen: CallScreenParams }, 'CallScreen'>;

const CallScreen: React.FC = () => {
  const route = useRoute<CallScreenRouteProp>();
  const navigation = useNavigation();
  const [callState, setCallState] = useState<CallState>(WebRTCService.getCallState());
  const [callDuration, setCallDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const callTimerRef = useRef<NodeJS.Timeout | null>(null);

  const { contact, isIncoming = false } = route.params || {};

  useEffect(() => {
    // Thiết lập callbacks cho WebRTC
    WebRTCService.setCallbacks({
      onCallStateChanged: (state: CallState) => {
        setCallState(state);
        
        // Bắt đầu timer khi cuộc gọi được kết nối
        if (state.isConnected && !callTimerRef.current) {
          startCallTimer();
        }
        
        // Kết thúc cuộc gọi - quay về màn hình trước
        if (!state.isInCall) {
          endCall();
        }
      },
      onCallAccepted: () => {
        console.log('Call accepted');
      },
      onCallRejected: () => {
        Alert.alert('Cuộc gọi bị từ chối', '', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      },
      onCallEnded: () => {
        endCall();
      },
      onError: (error) => {
        Alert.alert('Lỗi cuộc gọi', error.message || 'Có lỗi xảy ra', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      },
    });

    // Xử lý back button
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // Không cho phép back khi đang trong cuộc gọi
      return true;
    });

    return () => {
      backHandler.remove();
      if (callTimerRef.current) {
        clearInterval(callTimerRef.current);
      }
    };
  }, [navigation]);

  const startCallTimer = () => {
    callTimerRef.current = setInterval(() => {
      setCallDuration(prev => prev + 1);
    }, 1000);
  };

  const formatCallDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAcceptCall = async () => {
    try {
      await WebRTCService.acceptCall();
    } catch (error) {
      console.error('Error accepting call:', error);
      Alert.alert('Lỗi', 'Không thể chấp nhận cuộc gọi');
    }
  };

  const handleRejectCall = () => {
    WebRTCService.rejectCall();
    navigation.goBack();
  };

  const handleEndCall = () => {
    WebRTCService.endCall();
  };

  const endCall = () => {
    if (callTimerRef.current) {
      clearInterval(callTimerRef.current);
      callTimerRef.current = null;
    }
    navigation.goBack();
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    // TODO: Implement mute functionality
  };

  const toggleSpeaker = () => {
    setIsSpeakerOn(!isSpeakerOn);
    // TODO: Implement speaker functionality
  };

  const getCallStatusText = (): string => {
    if (callState.isConnected) {
      return formatCallDuration(callDuration);
    } else if (callState.isIncoming) {
      return 'Cuộc gọi đến...';
    } else if (callState.isOutgoing) {
      return 'Đang gọi...';
    }
    return 'Đang kết nối...';
  };

  const getContactInfo = () => {
    if (contact) {
      return {
        name: contact.Name,
        avatar: contact.AvatarUrl,
        id: contact.Id,
      };
    } else if (callState.targetUserName || callState.targetUserId) {
      return {
        name: callState.targetUserName || 'Người dùng',
        avatar: route.params?.callerAvatar,
        id: callState.targetUserId,
      };
    }
    return {
      name: 'Người dùng',
      avatar: null,
      id: null,
    };
  };

  const contactInfo = getContactInfo();

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={ColorThemes.light.primary_color} />
      
      {/* Header với thông tin người gọi */}
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          {contactInfo.avatar ? (
            <FastImage 
              source={{ uri: ConfigAPI.urlImg + contactInfo.avatar }} 
              style={styles.avatar} 
            />
          ) : (
            <View style={[styles.avatar, styles.defaultAvatar]}>
              <Text style={styles.avatarText}>
                {contactInfo.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
        </View>
        
        <Text style={styles.contactName}>{contactInfo.name}</Text>
        <Text style={styles.callStatus}>{getCallStatusText()}</Text>
      </View>

      {/* Control buttons */}
      <View style={styles.controlsContainer}>
        {callState.isIncoming && !callState.isConnected ? (
          // Incoming call controls
          <View style={styles.incomingControls}>
            <TouchableOpacity
              style={[styles.controlButton, styles.rejectButton]}
              onPress={handleRejectCall}
              activeOpacity={0.8}
            >
              <Winicon src="fill/user interface/phone-call" size={32} color="white" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.controlButton, styles.acceptButton]}
              onPress={handleAcceptCall}
              activeOpacity={0.8}
            >
              <Winicon src="fill/user interface/phone-call" size={32} color="white" />
            </TouchableOpacity>
          </View>
        ) : (
          // In-call controls
          <View style={styles.callControls}>
            <TouchableOpacity
              style={[styles.smallControlButton, isMuted && styles.activeControl]}
              onPress={toggleMute}
              activeOpacity={0.8}
            >
              <Winicon 
                src={isMuted ? "outline/multimedia/microphone-off" : "outline/sound/microphone-2"} 
                size={24} 
                color="white" 
              />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.controlButton, styles.endCallButton]}
              onPress={handleEndCall}
              activeOpacity={0.8}
            >
              <Winicon src="fill/user interface/phone-call" size={32} color="white" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.smallControlButton, isSpeakerOn && styles.activeControl]}
              onPress={toggleSpeaker}
              activeOpacity={0.8}
            >
              <Winicon 
                src={isSpeakerOn ? "fill/sound/volume-up" : "outline/multimedia/volume-2"} 
                size={24} 
                color="white" 
              />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#00000080',
    
  },
  header: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  avatarContainer: {
    marginBottom: 30,
  },
  avatar: {
    width: 150,
    height: 150,
    borderRadius: 75,
  },
  defaultAvatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 60,
    fontWeight: 'bold',
  },
  contactName: {
    fontSize: 28,
    fontWeight: '600',
    color: 'white',
    marginBottom: 10,
    textAlign: 'center',
  },
  callStatus: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  controlsContainer: {
    paddingBottom: 80,
    paddingHorizontal: 40,
  },
  incomingControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  callControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  controlButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  smallControlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  rejectButton: {
    backgroundColor: '#F44336',
    transform: [{ rotate: '135deg' }],
  },
  endCallButton: {
    backgroundColor: '#F44336',
    transform: [{ rotate: '135deg' }],
  },
  activeControl: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
});

export default CallScreen;

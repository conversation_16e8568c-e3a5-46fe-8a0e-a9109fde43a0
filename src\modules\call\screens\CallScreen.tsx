import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  Alert,
  BackHandler,
} from 'react-native';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { ColorThemes } from '../../../assets/skin/colors';
import { Winicon } from 'wini-mobile-components';
import WebRTCService, { CallState } from '../../../features/call/WebRTCService';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../../Config/ConfigAPI';

const { width, height } = Dimensions.get('window');

interface CallScreenParams {
  contact?: {
    Id: string;
    Name: string;
    AvatarUrl?: string;
  };
  isIncoming?: boolean;
  callerId?: string;
  callerName?: string;
  callerAvatar?: string;
}

type CallScreenRouteProp = RouteProp<{ CallScreen: CallScreenParams }, 'CallScreen'>;

const CallScreen: React.FC = () => {
  const route = useRoute<CallScreenRouteProp>();
  const navigation = useNavigation();
  const [callState, setCallState] = useState<CallState>(WebRTCService.getCallState());
  const [callDuration, setCallDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const callTimerRef = useRef<NodeJS.Timeout | null>(null);

  const { contact, isIncoming = false } = route.params || {};

  useEffect(() => {
    // Lấy current state từ WebRTC khi mount
    const currentWebRTCState = WebRTCService.getCallState();
    console.log('📞 CallScreen mounted, current WebRTC state:', currentWebRTCState);
    setCallState(currentWebRTCState);

    // Thiết lập callbacks cho WebRTC
    WebRTCService.setCallbacks({
      onCallStateChanged: (state: CallState) => {
        console.log('📞 CallScreen received state change:', JSON.stringify(state, null, 2));
        console.log('📞 Current timer ref:', callTimerRef.current);
        console.log('📞 Timer conditions:', {
          hasTimer: !!callTimerRef.current,
          isInCall: state.isInCall,
          isConnected: state.isConnected,
          isIncoming: state.isIncoming
        });

        setCallState(state);

        // Bắt đầu timer khi cuộc gọi được kết nối
        if (!callTimerRef.current && state.isInCall && state.isConnected) {
          console.log('📞 ✅ Starting timer - all conditions met');
          startCallTimer();
        } else if (!callTimerRef.current && state.isInCall && !state.isIncoming) {
          // Backup: start timer nếu call active và không phải incoming
          console.log('📞 ✅ Starting timer - backup condition (call active, not incoming)');
          startCallTimer();
        } else {
          console.log('📞 ❌ Not starting timer - conditions not met');
        }

        // Kết thúc cuộc gọi - quay về màn hình trước
        if (!state.isInCall) {
          endCall();
        }
      },
      onCallAccepted: () => {
        console.log('📞 Call accepted callback triggered');
        // Start timer ngay khi accept (backup method)
        if (!callTimerRef.current) {
          console.log('📞 Starting timer from onCallAccepted');
          startCallTimer();
        }
      },
      onCallRejected: () => {
        console.log('📞 Call was rejected by receiver');
        Alert.alert('Cuộc gọi bị từ chối', '', [
          { text: 'OK', onPress: () => {
            console.log('📞 User tapped OK on reject alert, calling endCall()');
            // Cleanup và quay về màn hình trước
            endCall();
          }}
        ]);
      },
      onCallEnded: () => {
        endCall();
      },
      onError: (error) => {
        Alert.alert('Lỗi cuộc gọi', error.message || 'Có lỗi xảy ra', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      },
    });

    // Xử lý back button
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // Không cho phép back khi đang trong cuộc gọi
      return true;
    });

    return () => {
      backHandler.remove();
      if (callTimerRef.current) {
        clearInterval(callTimerRef.current);
      }
    };
  }, [navigation]);

  // Manual test timer after 3 seconds
  useEffect(() => {
    const testTimer = setTimeout(() => {
      console.log('📞 Manual test: checking if timer should start');
      console.log('📞 Current state:', callState);
      console.log('📞 Has timer:', !!callTimerRef.current);

      if (!callTimerRef.current && callState.isInCall) {
        console.log('📞 Manual starting timer');
        startCallTimer();
      }
    }, 3000);

    return () => clearTimeout(testTimer);
  }, [callState]);

  const startCallTimer = () => {
    console.log('📞 startCallTimer() called');
    if (callTimerRef.current) {
      console.log('📞 Timer already exists, clearing first');
      clearInterval(callTimerRef.current);
    }

    console.log('📞 Starting new timer interval');
    callTimerRef.current = setInterval(() => {
      setCallDuration(prev => {
        const newDuration = prev + 1;
        console.log('📞 Timer tick:', newDuration);
        return newDuration;
      });
    }, 1000);
  };

  const formatCallDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAcceptCall = async () => {
    console.log('📞 handleAcceptCall called');
    try {
      console.log('📞 Calling WebRTCService.acceptCall()');
      await WebRTCService.acceptCall();
      console.log('📞 WebRTCService.acceptCall() completed');

      // Force start timer as backup
      setTimeout(() => {
        if (!callTimerRef.current) {
          console.log('📞 Force starting timer after accept');
          startCallTimer();
        }
      }, 1000);

    } catch (error) {
      console.error('Error accepting call:', error);
      Alert.alert('Lỗi', 'Không thể chấp nhận cuộc gọi');
    }
  };

  const handleRejectCall = () => {
    WebRTCService.rejectCall();
    navigation.goBack();
  };

  const handleEndCall = () => {
    console.log('📞 handleEndCall called');
    try {
      WebRTCService.endCall();
      console.log('📞 WebRTC endCall completed');
    } catch (error) {
      console.error('📞 Error ending call:', error);
    }

    // Cleanup và navigate back ngay lập tức (không chờ WebRTC callback)
    console.log('📞 Force cleanup and navigate');
    endCall();
  };

  const endCall = () => {
    console.log('📞 endCall() called, cleaning up timer and navigating back');
    if (callTimerRef.current) {
      clearInterval(callTimerRef.current);
      callTimerRef.current = null;
      console.log('📞 Timer cleared');
    }
    console.log('📞 Navigating back...');
    navigation.goBack();
  };

  const toggleMute = () => {
    const newMuteState = !isMuted;
    setIsMuted(newMuteState);
    console.log('📞 Toggling mute:', newMuteState);
    // TODO: Implement actual mute functionality with WebRTC
    // WebRTCService.toggleMute(newMuteState);
  };

  const toggleSpeaker = () => {
    const newSpeakerState = !isSpeakerOn;
    setIsSpeakerOn(newSpeakerState);
    console.log('📞 Toggling speaker:', newSpeakerState);
    // TODO: Implement actual speaker functionality
    // WebRTCService.toggleSpeaker(newSpeakerState);
  };

  const getCallStatusText = (): string => {
    if (callState.isConnected) {
      return formatCallDuration(callDuration);
    } else if (callState.isIncoming) {
      return 'Cuộc gọi đến...';
    } else if (callState.isOutgoing) {
      return 'Đang gọi...';
    }
    return 'Đang kết nối...';
  };

  const getContactInfo = () => {
    if (contact) {
      return {
        name: contact.Name,
        avatar: contact.AvatarUrl,
        id: contact.Id,
      };
    } else if (callState.targetUserName || callState.targetUserId) {
      return {
        name: callState.targetUserName || 'Người dùng',
        avatar: route.params?.callerAvatar,
        id: callState.targetUserId,
      };
    }
    return {
      name: 'Người dùng',
      avatar: null,
      id: null,
    };
  };

  const contactInfo = getContactInfo();

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={ColorThemes.light.primary_color} />
      
      {/* Header với thông tin người gọi */}
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          {contactInfo.avatar ? (
            <FastImage 
              source={{ uri: ConfigAPI.urlImg + contactInfo.avatar }} 
              style={styles.avatar} 
            />
          ) : (
            <View style={[styles.avatar, styles.defaultAvatar]}>
              <Text style={styles.avatarText}>
                {contactInfo.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
        </View>
        
        <Text style={styles.contactName}>{contactInfo.name}</Text>
        <Text style={styles.callStatus}>{getCallStatusText()}</Text>
      </View>

      {/* Control buttons */}
      <View style={styles.controlsContainer}>
        {/* Debug info */}
        <Text style={{ color: 'white', fontSize: 12, textAlign: 'center', marginBottom: 10 }}>
          Debug: isIncoming={callState.isIncoming ? 'true' : 'false'}, isConnected={callState.isConnected ? 'true' : 'false'}
        </Text>

        {/* Test button */}
        <TouchableOpacity
          style={{ backgroundColor: 'blue', padding: 10, marginBottom: 10 }}
          onPress={() => {
            console.log('📞 TEST BUTTON PRESSED');
            handleEndCall();
          }}
        >
          <Text style={{ color: 'white', textAlign: 'center' }}>TEST END CALL</Text>
        </TouchableOpacity>

        {callState.isIncoming ? (
          // Incoming call controls - chỉ hiển thị khi thực sự là incoming call chưa accept
          <View style={styles.incomingControls}>
            <TouchableOpacity
              style={[styles.controlButton, styles.rejectButton]}
              onPress={handleRejectCall}
              activeOpacity={0.8}
            >
              <Winicon src="fill/user interface/phone-call" size={32} color="white" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, styles.acceptButton]}
              onPress={handleAcceptCall}
              activeOpacity={0.8}
            >
              <Winicon src="fill/user interface/phone-call" size={32} color="white" />
            </TouchableOpacity>
          </View>
        ) : (
          // In-call controls - hiển thị mic, speaker, end call
          <View style={styles.callControls}>
            <TouchableOpacity
              style={[styles.smallControlButton, isMuted && styles.activeControl]}
              onPress={toggleMute}
              activeOpacity={0.8}
            >
              <Winicon
                src={isMuted ? "outline/multimedia/microphone-off" : "outline/sound/microphone-2"}
                size={24}
                color="white"
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.controlButton,
                styles.endCallButton,
                { borderWidth: 2, borderColor: 'yellow' } // Debug border
              ]}
              onPress={() => {
                console.log('📞 End call button pressed');
                handleEndCall();
              }}
              activeOpacity={0.8}
              onPressIn={() => console.log('📞 End call button press IN')}
              onPressOut={() => console.log('📞 End call button press OUT')}
            >
              <Winicon src="fill/user interface/phone-call" size={32} color="white" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.smallControlButton, isSpeakerOn && styles.activeControl]}
              onPress={toggleSpeaker}
              activeOpacity={0.8}
            >
              <Winicon
                src={isSpeakerOn ? "fill/sound/volume-up" : "outline/multimedia/volume-2"}
                size={24}
                color="white"
              />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#00000080',
    
  },
  header: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  avatarContainer: {
    marginBottom: 30,
  },
  avatar: {
    width: 150,
    height: 150,
    borderRadius: 75,
  },
  defaultAvatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 60,
    fontWeight: 'bold',
  },
  contactName: {
    fontSize: 28,
    fontWeight: '600',
    color: 'white',
    marginBottom: 10,
    textAlign: 'center',
  },
  callStatus: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  controlsContainer: {
    paddingBottom: 80,
    paddingHorizontal: 40,
  },
  incomingControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  callControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  controlButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  smallControlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  rejectButton: {
    backgroundColor: '#F44336',
    transform: [{ rotate: '135deg' }],
  },
  endCallButton: {
    backgroundColor: '#F44336',
    transform: [{ rotate: '135deg' }],
  },
  activeControl: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
});

export default CallScreen;

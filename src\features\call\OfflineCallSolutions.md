# Giải pháp cho User Offline khi nhận cuộc gọi

## 🎯 **Vấn đề:**
Khi user nhận cuộc gọi không online (app đóng, không có internet, hoặc device tắt), làm sao để thông báo và xử lý cuộc gọi?

## 💡 **Các giải pháp được đề xuất:**

### **1. Push Notification (Khuyến nghị)**

#### **A. Firebase Cloud Messaging (FCM)**
```typescript
// Server-side: Gửi push notification khi user offline
const sendCallNotification = async (targetUserId: string, callerData: any) => {
  const userToken = await getUserFCMToken(targetUserId);
  
  if (userToken) {
    const message = {
      token: userToken,
      notification: {
        title: `<PERSON>uộc gọi từ ${callerData.name}`,
        body: 'Nhấn để trả lời cuộc gọi',
      },
      data: {
        type: 'incoming_call',
        callerId: callerData.id,
        callerName: callerData.name,
        callerAvatar: callerData.avatar,
        callHistoryId: callerData.callHistoryId,
      },
      android: {
        priority: 'high',
        notification: {
          channelId: 'incoming_calls',
          priority: 'high',
          defaultSound: true,
          defaultVibrateTimings: true,
        },
      },
      apns: {
        payload: {
          aps: {
            alert: {
              title: `Cuộc gọi từ ${callerData.name}`,
              body: 'Nhấn để trả lời cuộc gọi',
            },
            sound: 'default',
            badge: 1,
          },
        },
      },
    };

    await admin.messaging().send(message);
  }
};
```

#### **B. Client-side: Xử lý push notification**
```typescript
// src/features/notifications/CallNotificationHandler.ts
import messaging from '@react-native-firebase/messaging';
import { navigate, RootScreen } from '../../router/router';

class CallNotificationHandler {
  static setupCallNotifications() {
    // Xử lý khi app đang chạy foreground
    messaging().onMessage(async remoteMessage => {
      if (remoteMessage.data?.type === 'incoming_call') {
        this.handleIncomingCallNotification(remoteMessage.data);
      }
    });

    // Xử lý khi app ở background/quit và user tap notification
    messaging().onNotificationOpenedApp(remoteMessage => {
      if (remoteMessage.data?.type === 'incoming_call') {
        this.handleCallNotificationTap(remoteMessage.data);
      }
    });

    // Xử lý khi app được mở từ quit state bởi notification
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage?.data?.type === 'incoming_call') {
          this.handleCallNotificationTap(remoteMessage.data);
        }
      });
  }

  static handleIncomingCallNotification(data: any) {
    // Hiển thị incoming call UI nếu app đang chạy
    IncomingCallOverlayService.showIncomingCall({
      callerName: data.callerName,
      callerAvatar: data.callerAvatar,
      callerId: data.callerId,
    });
  }

  static handleCallNotificationTap(data: any) {
    // Navigate đến CallScreen khi user tap notification
    navigate(RootScreen.CallScreen, {
      isIncoming: true,
      callerId: data.callerId,
      callerName: data.callerName,
      callerAvatar: data.callerAvatar,
    });
  }
}
```

### **2. CallKit (iOS) & ConnectionService (Android)**

#### **A. iOS CallKit Integration**
```typescript
// react-native-callkeep
import RNCallKeep from 'react-native-callkeep';

const setupCallKeep = () => {
  const options = {
    ios: {
      appName: 'Chainivo',
      maximumCallGroups: 1,
      maximumCallsPerCallGroup: 1,
      supportsVideo: false,
      includesCallsInRecents: true,
      imageName: 'sim_icon',
    },
    android: {
      alertTitle: 'Permissions required',
      alertDescription: 'This application needs to access your phone accounts',
      cancelButton: 'Cancel',
      okButton: 'ok',
      imageName: 'phone_account_icon',
      additionalPermissions: [],
      selfManaged: true,
    },
  };

  RNCallKeep.setup(options);
};

const displayIncomingCall = (callData: any) => {
  RNCallKeep.displayIncomingCall(
    callData.callId,
    callData.callerName,
    callData.callerName,
    'generic',
    false // hasVideo
  );
};
```

### **3. Background Service (Android)**

#### **A. Foreground Service cho call monitoring**
```kotlin
// Android Foreground Service
class CallMonitoringService : Service() {
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        
        // Monitor socket connection và incoming calls
        return START_STICKY
    }
    
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Chainivo Call Service")
            .setContentText("Monitoring incoming calls")
            .setSmallIcon(R.drawable.ic_phone)
            .build()
    }
}
```

### **4. Server-side Logic**

#### **A. Detect user online status**
```typescript
// Server: Kiểm tra user online status
const handleCallUser = (socket, data) => {
  const targetSocket = findUserSocket(data.targetUserId);
  
  if (targetSocket) {
    // User online - gửi trực tiếp qua socket
    targetSocket.emit('incoming-call', {
      from: socket.userId,
      fromName: data.fromName,
      callHistoryId: data.callHistoryId,
    });
  } else {
    // User offline - gửi push notification
    sendCallNotification(data.targetUserId, {
      id: socket.userId,
      name: data.fromName,
      callHistoryId: data.callHistoryId,
    });
    
    // Thông báo cho người gọi rằng user offline
    socket.emit('user-offline', {
      targetUserId: data.targetUserId,
    });
  }
};
```

#### **B. Call timeout handling**
```typescript
// Server: Xử lý timeout cho offline users
const callTimeouts = new Map();

const handleCallUser = (socket, data) => {
  // ... existing logic ...
  
  // Set timeout cho cuộc gọi
  const timeoutId = setTimeout(() => {
    // Notify caller về timeout
    socket.emit('call-timeout', {
      targetUserId: data.targetUserId,
    });
    
    // Update CallHistory
    updateCallHistory(data.callHistoryId, {
      EndReason: 'timeout',
    });
    
    callTimeouts.delete(data.callHistoryId);
  }, 60000); // 60 seconds
  
  callTimeouts.set(data.callHistoryId, timeoutId);
};
```

## 🔧 **Implementation Plan:**

### **Phase 1: Push Notifications (Ưu tiên cao)**
1. ✅ Setup FCM cho incoming calls
2. ✅ Tạo notification channel cho calls
3. ✅ Handle notification tap để mở app
4. ✅ Integrate với existing call system

### **Phase 2: CallKit Integration (Ưu tiên trung bình)**
1. ⏳ Setup react-native-callkeep
2. ⏳ Integrate với iOS CallKit
3. ⏳ Integrate với Android ConnectionService
4. ⏳ Handle system call UI

### **Phase 3: Background Service (Ưu tiên thấp)**
1. ⏳ Android foreground service
2. ⏳ Background socket monitoring
3. ⏳ Battery optimization handling

## 📱 **User Experience Flow:**

### **Scenario 1: User online**
1. Caller starts call → Socket event → Immediate notification
2. Receiver sees incoming call UI → Accept/Reject
3. Normal call flow

### **Scenario 2: User offline**
1. Caller starts call → Server detects offline → Push notification
2. User sees system notification → Taps notification
3. App opens → Navigate to CallScreen → Auto-accept or show UI
4. If too late → Show "Missed call" message

### **Scenario 3: User returns online**
1. App reconnects to socket
2. Check for missed calls from server
3. Show missed call notifications
4. Update CallHistory

## 🎯 **Recommended Solution:**

**Bắt đầu với Push Notifications (Phase 1)** vì:
- ✅ Dễ implement với FCM hiện có
- ✅ Cross-platform support
- ✅ Không cần permissions phức tạp
- ✅ Tích hợp tốt với existing system

**Sau đó mở rộng với CallKit** để có trải nghiệm native như các app gọi điện thật.

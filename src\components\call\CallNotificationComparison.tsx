import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView, ScrollView } from 'react-native';
import IncomingCallNotification from './IncomingCallNotification';
import FloatingIncomingCall from './FloatingIncomingCall';

// Component để so sánh 2 loại notification
const CallNotificationComparison: React.FC = () => {
  const [showOverlay, setShowOverlay] = useState(false);
  const [showFloating, setShowFloating] = useState(false);

  const handleAccept = (type: string) => {
    console.log(`📞 ${type} call accepted`);
    setShowOverlay(false);
    setShowFloating(false);
  };

  const handleReject = (type: string) => {
    console.log(`📞 ${type} call rejected`);
    setShowOverlay(false);
    setShowFloating(false);
  };

  const handleMinimize = () => {
    console.log('📞 Floating call minimized');
    setShowFloating(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Call Notification Comparison</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>1. Overlay Notification (Blocks Interaction)</Text>
          <Text style={styles.description}>
            Full overlay với background, blocks toàn bộ tương tác với app
          </Text>
          
          <TouchableOpacity 
            style={[styles.button, styles.overlayButton]} 
            onPress={() => setShowOverlay(true)}
          >
            <Text style={styles.buttonText}>Show Overlay Notification</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>2. Floating Notification (Non-blocking)</Text>
          <Text style={styles.description}>
            Floating card ở top, không block tương tác với app. Nhấn giữ để ẩn.
          </Text>
          
          <TouchableOpacity 
            style={[styles.button, styles.floatingButton]} 
            onPress={() => setShowFloating(true)}
          >
            <Text style={styles.buttonText}>Show Floating Notification</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Test Interaction</Text>
          <Text style={styles.description}>
            Thử tương tác với các elements này khi notification đang hiển thị:
          </Text>
          
          <TouchableOpacity style={styles.testButton} onPress={() => console.log('Button 1 pressed')}>
            <Text style={styles.testButtonText}>Test Button 1</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.testButton} onPress={() => console.log('Button 2 pressed')}>
            <Text style={styles.testButtonText}>Test Button 2</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.testButton} onPress={() => console.log('Button 3 pressed')}>
            <Text style={styles.testButtonText}>Test Button 3</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.comparison}>
          <Text style={styles.comparisonTitle}>Comparison:</Text>
          
          <View style={styles.comparisonRow}>
            <Text style={styles.feature}>App Interaction:</Text>
            <Text style={styles.overlayResult}>❌ Blocked</Text>
            <Text style={styles.floatingResult}>✅ Allowed</Text>
          </View>
          
          <View style={styles.comparisonRow}>
            <Text style={styles.feature}>Visual Impact:</Text>
            <Text style={styles.overlayResult}>✅ High</Text>
            <Text style={styles.floatingResult}>⚠️ Medium</Text>
          </View>
          
          <View style={styles.comparisonRow}>
            <Text style={styles.feature}>User Control:</Text>
            <Text style={styles.overlayResult}>❌ Limited</Text>
            <Text style={styles.floatingResult}>✅ Full</Text>
          </View>
        </View>
      </ScrollView>

      {/* Overlay Notification */}
      <IncomingCallNotification
        visible={showOverlay}
        callerName="John Doe (Overlay)"
        callerAvatar={undefined}
        onAccept={() => handleAccept('Overlay')}
        onReject={() => handleReject('Overlay')}
      />

      {/* Floating Notification */}
      <FloatingIncomingCall
        visible={showFloating}
        callerName="Jane Smith (Floating)"
        callerAvatar={undefined}
        onAccept={() => handleAccept('Floating')}
        onReject={() => handleReject('Floating')}
        onMinimize={handleMinimize}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  section: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  button: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  overlayButton: {
    backgroundColor: '#FF6B6B',
  },
  floatingButton: {
    backgroundColor: '#4ECDC4',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  testButton: {
    backgroundColor: '#3498DB',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  comparison: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
  },
  comparisonTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
    color: '#333',
  },
  comparisonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  feature: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  overlayResult: {
    flex: 1,
    fontSize: 14,
    textAlign: 'center',
  },
  floatingResult: {
    flex: 1,
    fontSize: 14,
    textAlign: 'center',
  },
});

export default CallNotificationComparison;

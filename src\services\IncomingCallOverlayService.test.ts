// Test file để kiểm tra IncomingCallOverlayService
import IncomingCallOverlayService from './IncomingCallOverlayService';

// Test basic functionality
console.log('📞 Testing IncomingCallOverlayService...');

// Test 1: Add listener
const testListener = (data: any) => {
  console.log('📞 Test listener received:', data);
};

IncomingCallOverlayService.on('test', testListener);
console.log('📞 Listener count after add:', IncomingCallOverlayService.listenerCount('test'));

// Test 2: Emit event
IncomingCallOverlayService.emit('test', { message: 'Hello World' });

// Test 3: Remove listener
IncomingCallOverlayService.off('test', testListener);
console.log('📞 Listener count after remove:', IncomingCallOverlayService.listenerCount('test'));

// Test 4: Emit after remove (should not trigger)
IncomingCallOverlayService.emit('test', { message: 'Should not appear' });

console.log('📞 IncomingCallOverlayService test completed');

export {};

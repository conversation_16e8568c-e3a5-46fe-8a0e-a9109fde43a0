import React from 'react';
import {View, StyleSheet, TouchableOpacity, Text} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';

const ActionBar = ({
  data,
  handleToggleLike,
}: {
  handleToggleLike: (isCurrentlyLiked: boolean) => void;
  data: {
    Likes?: number;
    Comments?: number;
    Views?: number;
    IsLike: boolean;
  };
}) => {
  return (
    <View style={styles.engagementBar}>
      <TouchableOpacity
        onPress={() => handleToggleLike(data.IsLike)}
        style={styles.engagementItem}>
        <AppSvg
          SvgSrc={iconSvg.likeColorYellow}
          size={20}
          color={
            data.IsLike
              ? ColorThemes.light.secondary5_sub_color
              : ColorThemes.light.neutral_text_subtitle_color
          }
        />
        <Text
          style={[
            styles.engagementText,
            {color: ColorThemes.light.secondary5_sub_color},
          ]}>
          {data.Likes || 0}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.engagementItem}>
        <AppSvg SvgSrc={iconSvg.commentColor} size={20} />
        <Text
          style={[
            styles.engagementText,
            {color: ColorThemes.light.primary_darker_color},
          ]}>
          {data.Comments || 0}
        </Text>
      </TouchableOpacity>
      <View style={styles.engagementItem}>
        <AppSvg
          SvgSrc={iconSvg.eyeColor}
          size={20}
          color={ColorThemes.light.neutral_text_subtitle_color}
        />
        <Text
          style={[
            styles.engagementText,
            {color: ColorThemes.light.primary_darker_color},
          ]}>
          {data.Views || 0}
        </Text>
      </View>
      <TouchableOpacity style={styles.engagementItem}>
        <Text style={styles.shareText}>Chia sẻ</Text>
        <AppSvg SvgSrc={iconSvg.shareOutline} size={20} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  engagementBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  engagementItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  engagementText: {
    ...TypoSkin.title5,
    marginLeft: 4,
  },
  shareText: {
    ...TypoSkin.title5,
    color: ColorThemes.light.primary_darker_color,
    marginRight: 4,
  },
});

export default ActionBar;

import React, {useCallback, useRef, useState} from 'react';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ScrollView, FlatList} from 'react-native-gesture-handler';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useRoute} from '@react-navigation/native';
import {useForm} from 'react-hook-form';
import {useDispatch} from 'react-redux';
import FastImage from 'react-native-fast-image';
import RenderHTML from 'react-native-render-html';

// Components
import {
  Winicon,
  AppButton,
  SkeletonImage,
  ListTile,
  FDialog,
  FLoading,
  FBottomSheet,
  showBottomSheet,
  hideBottomSheet,
} from 'wini-mobile-components';
import ClickableImage from '../../../components/ClickableImage';
import CommentsListNews from '../../customer/listview/commentsNews';
import EmptyPage from '../../../Screen/emptyPage';
import YouTubePlayer from '../../../utils/YouTubeWebViewPlayer';
import {TextFieldForm} from '../../Default/form/component-form';

// Utils & Config
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {navigate, navigateBack, RootScreen} from '../../../router/router';
import ConfigAPI from '../../../Config/ConfigAPI';
import {onShare} from '../../../features/share';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {extractVideoId} from '../card/defaultPost';

// Redux
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import {postCommentsActions} from '../reducers/postCommentsReducer';
import store, {AppDispatch} from '../../../redux/store/store';
import {DataController} from '../../../base/baseController';

export default function PostDetail() {
  // Hooks
  const route = useRoute<any>();
  const {item, forNew} = route?.params;
  const dispatch: AppDispatch = useDispatch();
  const user = useSelectorCustomerState().data;
  const methods = useForm<any>({shouldFocusError: false});
  const [isLoading, setIsLoading] = useState(false);

  // State
  const [data, setItem] = useState<any>(item);

  // Refs
  const scrollViewRef = useRef<ScrollView>(null);
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);

  React.useEffect(() => {
    const getLikes = async () => {
      if (!data?.Id) return;
      setIsLoading(true);
      try {
        const likeController = new DataController('Like');
        const response = await likeController.getPatternList({
          query: forNew ? `@NewsId:{${data.Id}}` : `@PostId:{${data.Id}}`,
          pattern: {
            CustomerId: ['Id', 'Name', 'AvatarUrl'],
          },
        });

        if (response.code === 200) {
          const likes = response.data || [];
          const customers = response.Customer || [];

          // Check if current user has liked this post
          const userLike = user?.Id
            ? likes.find((like: any) => like.CustomerId === user.Id)
            : null;

          setItem((prevData: any) => ({
            ...prevData,
            Likes: likes,
            LikesCount: likes.length,
            IsLike: !!userLike,
            CustomerLiked: customers,
          }));
        }
      } catch (error) {
        console.error('Error fetching likes:', error);
      }
      setIsLoading(false);
    };

    getLikes();
  }, [item, forNew, user?.Id]);

  // Xử lý danh sách ảnh
  const imageList = React.useMemo(() => {
    if (!data?.Img) return [];

    // Nếu Img chứa dấu phẩy, tách thành mảng các ảnh
    if (data.Img.includes(',')) {
      return data.Img.split(',')
        .filter((img: string) => img && img.trim() !== '')
        .map((img: string) => ConfigAPI.urlImg + img.trim());
    }

    // Nếu chỉ có một ảnh
    return [ConfigAPI.urlImg + data.Img];
  }, [data?.Img]);

  // Cấu hình FastImage
  const fastImageProps = React.useMemo(
    () => ({
      priority: FastImage.priority.normal,
      cache: FastImage.cacheControl.immutable,
      resizeMode: FastImage.resizeMode.cover,
    }),
    [],
  );

  const handleToggleLike = useCallback(
    async (isCurrentlyLiked: boolean) => {
      if (!user || !data?.Id) {
        return;
      }

      const newLikeState = !isCurrentlyLiked;

      // Optimistic update
      setItem((prevData: any) => ({
        ...prevData,
        IsLike: newLikeState,
        Likes: isCurrentlyLiked
          ? prevData.Likes.filter((like: any) => like.CustomerId !== user.Id)
          : [...prevData.Likes, {CustomerId: user.Id}],
        LikesCount: isCurrentlyLiked
          ? Math.max(0, (prevData.LikesCount || 0) - 1)
          : (prevData.LikesCount || 0) + 1,
      }));

      try {
        // Update server
        await dispatch(
          newsFeedActions.updateLike(data.Id, isCurrentlyLiked, forNew),
        );

        // Update other feeds if they exist
        const newfeed = store
          .getState()
          .newsFeed.data.find((item: any) => item.Id === data.Id);
        if (newfeed) {
          dispatch(newsFeedActions.setLike(newfeed.Id, newLikeState));
        }
      } catch (error) {
        console.error('Failed to update like:', error);
        // Revert optimistic update on error
        setItem((prevData: any) => ({
          ...prevData,
          IsLike: isCurrentlyLiked,
          LikesCount: isCurrentlyLiked
            ? (prevData.LikesCount || 0) + 1
            : Math.max(0, (prevData.LikesCount || 0) - 1),
        }));
      }
    },
    [dispatch, data?.Id, forNew, user],
  );

  const handleReply = useCallback(
    async (dataComment?: any) => {
      if (dataComment) {
        methods.setValue('Comment', undefined);
        methods.setValue('Comment', `@${dataComment.relativeUser.title} `);
        methods.setValue('CommentId', `${dataComment.Id}`);
        methods.setValue('UserComment', `${dataComment.relativeUser.title}`);
        // Focus the comment input field
      } else {
        methods.setValue('Comment', undefined);
        methods.setValue('CommentId', undefined);
        methods.setValue('UserComment', undefined);
      }
    },
    [methods],
  );

  const handleAddComment = async () => {
    if (user) {
      if (methods.getValues().Comment) {
        await dispatch(
          postCommentsActions.addNewComment(
            data?.Id,
            methods.getValues().Comment,
            methods.getValues().CommentId?.toString().trim() || undefined,
          ),
        );

        setItem((prevData: any) => {
          return {
            ...prevData,
            Comment: prevData.Comment + 1,
          };
        });

        if (
          methods.getValues().CommentId === undefined ||
          methods.getValues().CommentId === ''
        ) {
          scrollViewRef.current?.scrollToEnd({
            animated: true,
          });
        }

        methods.setValue('Comment', '');
        methods.setValue('CommentId', '');
        methods.setValue('UserComment', '');

        dispatch(newsFeedActions.updateCommentCount(data?.Id, 1));
        const newfeed = store
          .getState()
          .newsFeed.data.find((item: any) => item.Id === data?.Id);
        if (newfeed) {
          dispatch(newsFeedActions.updateCommentCount(newfeed.Id, 1));
        }
      }
    } else {
      ///TODO: check chưa login thì confirm ra trang login
      dialogCheckAcc(dialogRef);
    }
  };
  return (
    <SafeAreaView
      edges={['top']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={bottomSheetRef} />
      <FLoading
        visible={isLoading}
        avt={require('../../../assets/appstore.png')}
      />
      <ListTile
        style={{paddingHorizontal: 16, padding: 0, paddingBottom: 16}}
        isClickLeading
        onPress={() => {
          if (!data?.relativeUser?.title && !data?.relativeUser?.image) return;
          navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
        }}
        leading={
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <TouchableOpacity
              style={{
                paddingRight: 16,
                paddingVertical: 8,
              }}
              onPress={() => navigateBack()}>
              <Winicon src="outline/arrows/left-arrow" size={20} />
            </TouchableOpacity>
            {!data?.relativeUser?.title &&
            !data?.relativeUser?.image ? null : data?.relativeUser?.image ? (
              <FastImage
                key={data?.relativeUser?.image}
                source={{
                  uri: data?.relativeUser?.image.includes('https')
                    ? data?.relativeUser?.image
                    : ConfigAPI.urlImg + data?.relativeUser?.image,
                }}
                style={{
                  width: 48,
                  height: 48,
                  borderRadius: 50,
                  backgroundColor: '#f0f0f0',
                }}
              />
            ) : (
              <View
                style={{
                  width: 32,
                  height: 32,
                  borderRadius: 50,
                  backgroundColor: ColorThemes.light.primary_main_color,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.neutral_absolute_background_color,
                  }}>
                  {data?.relativeUser?.title
                    ? data?.relativeUser?.title.charAt(0).toUpperCase()
                    : ''}
                </Text>
              </View>
            )}
          </View>
        }
        title={
          <Text
            style={{
              ...TypoSkin.heading7,
              color: ColorThemes.light.neutral_text_title_color,
            }}
            numberOfLines={1}>
            {data?.relativeUser?.title ?? ''}
          </Text>
        }
        subtitle={
          <Text
            onPress={() => {
              if (!data?.relativeUser?.title && !data?.relativeUser?.image)
                return;
              navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
            }}
            style={{
              ...TypoSkin.subtitle3,
              color: ColorThemes.light.neutral_text_subtitle_color,
            }}
            numberOfLines={1}>
            {data?.relativeUser?.subtitle ?? ''}
          </Text>
        }
        subTitleStyle={{
          ...TypoSkin.subtitle3,
          color: ColorThemes.light.neutral_text_subtitle_color,
        }}
        trailing={
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 4,
            }}>
            <AppButton
              backgroundColor={ColorThemes.light.neutral_main_background_color}
              borderColor="transparent"
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  title: 'Actions',
                  suffixAction: <View />,
                  prefixAction: (
                    <TouchableOpacity
                      onPress={() => hideBottomSheet(bottomSheetRef)}
                      style={{padding: 6, alignItems: 'center'}}>
                      <Winicon
                        src="outline/layout/xmark"
                        size={20}
                        color={ColorThemes.light.neutral_text_body_color}
                      />
                    </TouchableOpacity>
                  ),
                  children: (
                    <View
                      style={{
                        gap: 8,
                        height: Dimensions.get('window').height / 4,
                        width: '100%',
                        backgroundColor:
                          ColorThemes.light.neutral_absolute_background_color,
                      }}>
                      {data.CustomerId === user.Id && (
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);

                            navigate(RootScreen.createPost, {
                              editPost: data,
                              groupId: null,
                            });
                          }}
                          title={'Edit post'}
                          titleStyle={{...TypoSkin.body3}}
                        />
                      )}
                      {data.CustomerId === user.Id && (
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);
                            dispatch(newsFeedActions.deletePost(data));
                          }}
                          title={'Delete post'}
                          titleStyle={{...TypoSkin.body3}}
                        />
                      )}
                    </View>
                  ),
                });
              }}
              containerStyle={{
                borderRadius: 100,
                padding: 6,
                height: 24,
                width: 24,
              }}
              title={
                <Winicon
                  src={'fill/user interface/menu-dots'}
                  size={14}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
              }
            />
          </View>
        }
      />
      {/* contents */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'height' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0}
        // Add these props for smoother animation
        contentContainerStyle={{flex: 1}}
        style={{flex: 1}}>
        <ScrollView
          ref={scrollViewRef}
          style={{
            flex: 1,
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          {/* content */}
          <View style={{flex: 1, paddingHorizontal: 16}}>
            {data?.LinkVideo ? (
              <View style={{marginBottom: 16}}>
                <YouTubePlayer
                  videoId={extractVideoId(data?.LinkVideo) || ''}
                  height={200}
                  width={'100%'}
                  play={false}
                  useNativeControls={true}
                  playerParams={{
                    modestbranding: true,
                  }}
                />
              </View>
            ) : null}
            {/* img */}
            {data?.Content ? (
              <View style={{}}>
                <RenderHTML
                  contentWidth={Dimensions.get('screen').width}
                  source={{html: data?.Content}}
                  tagsStyles={{
                    body: {margin: 0, padding: 0},
                    div: {margin: 0, padding: 0},
                    p: {margin: 0, marginBottom: 8},
                    b: {fontWeight: 'bold'},
                    i: {fontStyle: 'italic'},
                    u: {textDecorationLine: 'underline'},
                  }}
                />
              </View>
            ) : null}
            {imageList.length > 0 ? (
              imageList.length === 1 ? (
                // Trường hợp chỉ có 1 ảnh - giữ nguyên hiển thị như cũ
                <View
                  style={[
                    {
                      height: 234,
                      width: '100%',
                      marginTop: 4,
                    },
                  ]}>
                  <ClickableImage
                    source={{
                      uri:
                        imageList[0] ||
                        'https://placehold.co/234/FFFFFF/000000/png',
                      ...fastImageProps,
                    }}
                    style={{
                      width: '100%',
                      height: 234,
                      borderRadius: 8,
                    }}
                  />
                </View>
              ) : (
                // Trường hợp có nhiều ảnh - hiển thị theo chiều dọc
                <View>
                  <FlatList
                    data={imageList}
                    scrollEnabled={false}
                    renderItem={({item: imageUri}) => (
                      <View style={{marginTop: 4}}>
                        <ClickableImage
                          source={{
                            uri: imageUri,
                            ...fastImageProps,
                          }}
                          style={{
                            width: '100%',
                            height: 234,
                            borderRadius: 8,
                          }}
                        />
                      </View>
                    )}
                    keyExtractor={(_, index) => `image-${index}`}
                  />
                </View>
              )
            ) : null}
          </View>
          {/* actions */}
          <View
            style={{
              flexDirection: 'row',
              paddingTop: 16,
              paddingHorizontal: 16,
              alignItems: 'center',
              gap: 8,
              borderBottomWidth: 1,
              borderBottomColor: ColorThemes.light.neutral_main_border_color,
              paddingBottom: 16,
            }}>
            <AppButton
              backgroundColor={ColorThemes.light.neutral_main_background_color}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              title={
                <Text
                  style={{
                    ...TypoSkin.buttonText5,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  }}>
                  {data?.LikesCount ?? 0}
                </Text>
              }
              textColor={
                data?.IsLike === true
                  ? ColorThemes.light.error_main_color
                  : ColorThemes.light.neutral_text_subtitle_color
              }
              onPress={() => handleToggleLike(data?.IsLike ?? false)}
              prefixIconSize={12}
              prefixIcon={
                data?.IsLike === true
                  ? 'fill/emoticons/heart'
                  : 'outline/emoticons/heart'
              }
            />
            <AppButton
              backgroundColor={ColorThemes.light.neutral_main_background_color}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              prefixIcon={'outline/user interface/b-comment'}
              prefixIconSize={12}
              textColor={ColorThemes.light.neutral_text_subtitle_color}
              title={
                <Text
                  style={{
                    ...TypoSkin.buttonText5,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  }}>
                  {data?.Comment ?? 0}
                </Text>
              }
            />
            {/* <AppButton
              backgroundColor={ColorThemes.light.neutral_main_background_color}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              onPress={async () => {
                onShare({content: 'Hello world'});
              }}
              prefixIcon={'fill/arrows/social-sharing'}
              prefixIconSize={12}
              textColor={ColorThemes.light.neutral_text_subtitle_color}
            /> */}
          </View>
          {/* likes */}
          {data?.CustomerLiked && data.CustomerLiked.length > 0 ? (
            <TouchableOpacity
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  enableDismiss: true,
                  title: 'Danh sách người đã thích',
                  children: (
                    <View
                      style={{
                        height: Dimensions.get('window').height / 1.66,
                        width: '100%',
                        backgroundColor:
                          ColorThemes.light.neutral_absolute_background_color,
                      }}>
                      <Pressable
                        style={{
                          flex: 1,
                          backgroundColor:
                            ColorThemes.light.neutral_absolute_background_color,
                        }}>
                        <FlatList
                          data={data?.CustomerLiked}
                          nestedScrollEnabled
                          style={{
                            height: '100%',
                            backgroundColor:
                              ColorThemes.light
                                .neutral_absolute_background_color,
                          }}
                          keyExtractor={(_, index) => index.toString()}
                          ListEmptyComponent={() => {
                            return <EmptyPage title="Không có dữ liệu" />;
                          }}
                          ListFooterComponent={() => {
                            return <View style={{height: 32}} />;
                          }}
                          renderItem={({item}) => {
                            return (
                              <ListTile
                                key={item?.Id}
                                onPress={() => {
                                  hideBottomSheet(bottomSheetRef);
                                  navigate(RootScreen.ProfileCommunity, {
                                    Id: item?.Id,
                                  });
                                }}
                                listtileStyle={{gap: 8}}
                                leading={
                                  <SkeletonImage
                                    source={{
                                      uri: item?.AvatarUrl
                                        ? `${
                                            ConfigAPI.urlImg + item?.AvatarUrl
                                          }`
                                        : 'https://placehold.co/48/FFFFFF/000000/png',
                                    }}
                                    style={{
                                      width: 48,
                                      height: 48,
                                      borderRadius: 50,
                                      backgroundColor: '#f0f0f0',
                                    }}
                                  />
                                }
                                title={
                                  <Text
                                    style={{
                                      ...TypoSkin.heading7,
                                      color:
                                        ColorThemes.light
                                          .neutral_text_title_color,
                                    }}
                                    numberOfLines={1}>
                                    {item?.Name}
                                  </Text>
                                }
                              />
                            );
                          }}
                        />
                      </Pressable>
                    </View>
                  ),
                });
              }}
              style={{padding: 16}}>
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>
                {data?.CustomerLiked?.length == 1
                  ? `${data?.CustomerLiked[0].Name} đã thích bài viết này`
                  : `${data?.CustomerLiked?.length} người đã thích bài viết này`}
              </Text>
            </TouchableOpacity>
          ) : null}
          {/* comments */}
          <CommentsListNews postId={data?.Id} onReply={handleReply} />
        </ScrollView>
      </KeyboardAvoidingView>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        <View
          style={{
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
            paddingHorizontal: 16,
            marginBottom: 32,
            paddingTop: 8,
            borderTopWidth: 1,
            borderTopColor: ColorThemes.light.neutral_main_border_color,
          }}>
          {methods.watch('CommentId') ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 4,
              }}>
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>
                Reply to {methods.getValues().UserComment ?? ''}
              </Text>
              <Text
                onPress={() => {
                  methods.setValue('CommentId', undefined);
                  methods.setValue('Comment', undefined);
                  methods.setValue('UserComment', undefined);
                }}
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>
                - Hủy
              </Text>
            </View>
          ) : null}
          <View
            style={{
              flexDirection: 'row',
              gap: 8,
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TextFieldForm
              textFieldStyle={{
                padding: 16,
                height: 40,
                paddingVertical: 0,
              }}
              style={{
                flex: 1,
              }}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              placeholder="Viết bình luận của bạn"
              name="Comment"
            />
            <AppButton
              prefixIcon={'fill/user interface/send-message'}
              prefixIconSize={24}
              backgroundColor={ColorThemes.light.transparent}
              borderColor="transparent"
              containerStyle={{
                paddingHorizontal: 12,
                height: 45,
              }}
              onPress={handleAddComment}
              textColor={ColorThemes.light.primary_main_color}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

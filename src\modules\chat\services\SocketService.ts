import io, { Socket } from 'socket.io-client';
import { ChatMessage, ChatRoom } from '../types/ChatTypes';
import ConfigAPI from '../../../Config/ConfigAPI';
import SocketDebugger from '../../../utils/SocketDebugger';

class SocketService {
  private socket: Socket | null = null;
  private isConnected: boolean = false;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(CustomerId: string,Name: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Sử dụng ConfigAPI.Socketurl thay vì ConfigAPI.url
        console.log('Connecting to socket:', ConfigAPI.Socketurl);
        console.log('Connecting to socket CustomerId:', CustomerId);
        this.socket = io(ConfigAPI.Socketurl, {
          auth: {
            CustomerId,
            Name,
          },
          transports: ['websocket'],
          timeout: 20000,
          forceNew: true,
          // Reconnection settings - disable auto-reconnection, we handle it manually
          reconnection: false,
        });

        this.socket.on('connect', () => {
          console.log('✅ Socket connected successfully');
          this.isConnected = true;

          // Reset reconnect attempts on successful connection
          this.reconnectAttempts = 0;
          this.clearReconnectTimeout();

          // Debug: Log connection
          if (this.socket?.id) {
            SocketDebugger.logConnection(this.socket.id, CustomerId, Name);
          }

          resolve();
        });

        this.socket.on('disconnect', (reason) => {
          console.log('🔌 Socket disconnected, reason:', reason);
          this.isConnected = false;

          // Debug: Log disconnection
          if (this.socket?.id) {
            SocketDebugger.logDisconnection(this.socket.id);
          }

          // Handle different disconnect reasons
          const SocketConnectionManager = require('../../../utils/SocketConnectionManager').default;

          if (reason === 'ping timeout') {
            console.log('⚠️ Socket disconnected due to ping timeout - will auto-reconnect');
            // Don't reset connection state, let it auto-reconnect
            this.scheduleReconnect();
          } else if (reason === 'transport close' || reason === 'transport error') {
            console.log('⚠️ Socket disconnected due to network issue - will auto-reconnect');
            this.scheduleReconnect();
          } else if (reason !== 'io client disconnect') {
            // Server disconnected us for other reasons
            console.log('⚠️ Server disconnected socket, clearing connection state');
            SocketConnectionManager.reset();
          }
        });

        this.socket.on('connect_error', (error) => {
          console.error('❌ Socket connection error:', error);
          this.isConnected = false;
          reject(error);
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  disconnect() {
    if (this.socket) {
      console.log('🔌 Disconnecting socket...');
      this.socket.removeAllListeners();
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      console.log('✅ Socket disconnected and cleaned up');
    }

    // Clear any pending reconnect
    this.clearReconnectTimeout();
  }

  /**
   * Schedule automatic reconnection after disconnect
   */
  private scheduleReconnect() {
    // Clear any existing reconnect timeout
    this.clearReconnectTimeout();

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ Max reconnect attempts reached, giving up');
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000); // Exponential backoff, max 30s
    console.log(`🔄 Scheduling reconnect attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts} in ${delay}ms`);

    this.reconnectTimeout = setTimeout(() => {
      this.attemptReconnect();
    }, delay);
  }

  /**
   * Attempt to reconnect using SocketConnectionManager
   */
  private async attemptReconnect() {
    try {
      this.reconnectAttempts++;
      console.log(`🔄 Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

      const SocketConnectionManager = require('../../../utils/SocketConnectionManager').default;
      const currentUserId = SocketConnectionManager.getCurrentUserId();

      if (currentUserId) {
        const success = await SocketConnectionManager.connect(currentUserId, '', true); // Force reconnect
        if (success) {
          console.log('✅ Reconnect successful');
          this.reconnectAttempts = 0; // Reset counter on success
        } else {
          console.log('❌ Reconnect failed, will retry...');
          this.scheduleReconnect();
        }
      } else {
        console.log('❌ No user ID available for reconnect');
      }
    } catch (error) {
      console.error('❌ Reconnect error:', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Clear reconnect timeout
   */
  private clearReconnectTimeout() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  joinRoom(roomId: string) {
    if (this.socket && this.isConnected) {
      console.log('Joining room:', roomId);
      this.socket.emit('join-rooms', { roomId });
    }
  }

  leaveRoom(roomId: string) {
    if (this.socket && this.isConnected) {
      console.log('Leaving room:', roomId);
      this.socket.emit('leave-rooms', { roomId });
    }
  }

  sendMessage(roomId: string, message: any, targetCustomerId: string[]) {
    if (this.socket && this.isConnected) {
      console.log('Sending message to room:', roomId, message);
      this.socket.emit('send-message', {
        roomId,
        message,
        targetCustomerId,
      });
    }
  }
  onError(callback: (error: any) => void) {
    if (this.socket) {
      this.socket.on('error', callback);
    }
  }

  onReceiveMessage(callback: (data: { roomId: string; fromUserId: string; message: any }) => void) {
    if (this.socket) {
      this.socket.on('receive-message', callback);
    }
  }

  onUserTyping(callback: (data: { roomId: string; fromUserId: string }) => void) {
    if (this.socket) {
      this.socket.on('typing', callback);
    }
  }

  sendTyping(roomId: string) {
    if (this.socket && this.isConnected) {
      console.log('Sending typing to room:', roomId);
      this.socket.emit('typing', { roomId });
    }
  }

  onOnlineUsers(callback: (users: any[]) => void) {
    if (this.socket) {
      this.socket.on('onlineUsers', callback);
    }
  }

  onUserOnline(callback: (customerId: string) => void) {
    if (this.socket) {
      this.socket.on('user-online', callback);
    }
  }

  onUserOffline(callback: (customerId: string) => void) {
    if (this.socket) {
      this.socket.on('user-offline', callback);
    }
  }

  // Read receipt methods
  sendReadReceipt(roomId: string, messageId: string) {
    if (this.socket && this.isConnected) {
      console.log('Sending read receipt for message:', messageId, 'in room:', roomId);
      this.socket.emit('message-read', {
        roomId,
        messageId,
        readAt: new Date().getTime()
      });
    }
  }

  onMessageRead(callback: (data: { roomId: string; messageId: string; readBy: string; readAt: number }) => void) {
    if (this.socket) {
      this.socket.on('message-read', callback);
    }
  }

  removeAllListeners() {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  getSocket(): Socket | null {
    return this.socket;
  }
}

export default new SocketService();

import React, { useEffect, useState } from 'react';
import { Modal } from 'react-native';
import IncomingCallNotification from './IncomingCallNotification';
import IncomingCallOverlayService from '../../services/IncomingCallOverlayService';
import WebRTCService from '../../features/call/WebRTCService';
import { navigate, RootScreen } from '../../router/router';

interface CallData {
  callerName: string;
  callerAvatar?: string;
  callerId: string;
}

const IncomingCallProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [showNotification, setShowNotification] = useState(false);
  const [callData, setCallData] = useState<CallData | null>(null);
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Listen for incoming call events
    const handleShowIncomingCall = (data: CallData) => {
      setCallData(data);
      setShowNotification(true);

      // Set timeout để tự động ẩn notification sau 60 giây
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        console.log('📞 Notification timeout, hiding notification');
        setShowNotification(false);
        setCallData(null);
      }, 60000); // 60 giây
    };

    const handleHideIncomingCall = () => {
      // Clear timeout khi ẩn notification
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      setShowNotification(false);
      setCallData(null);
    };

    // Listen for WebRTC call state changes để auto-hide notification
    const handleCallStateChanged = (state: any) => {
      // Nếu cuộc gọi kết thúc (không còn trong call), ẩn notification
      if (!state.isInCall && showNotification) {
        console.log('📞 Call ended, hiding notification');
        setShowNotification(false);
        setCallData(null);
      }
    };

    const handleAcceptCall = async (data: CallData) => {
      try {
        // Navigate to CallScreen
        navigate(RootScreen.CallScreen, {
          isIncoming: true,
          callerId: data.callerId,
          callerName: data.callerName,
          callerAvatar: data.callerAvatar,
        });

        // Accept call through WebRTC
        await WebRTCService.acceptCall();
      } catch (error) {
        console.error('Error accepting call:', error);
      }
    };

    const handleRejectCall = () => {
      WebRTCService.rejectCall();
    };

    // Setup WebRTC callback để listen call state changes
    WebRTCService.setCallbacks({
      onCallStateChanged: handleCallStateChanged,
      onCallEnded: () => {
        console.log('📞 WebRTC call ended, hiding notification');
        handleHideIncomingCall();
      },
      onCallRejected: () => {
        console.log('📞 Call rejected, hiding notification');
        handleHideIncomingCall();
      },
    });

    // Register event listeners
    IncomingCallOverlayService.on('showIncomingCall', handleShowIncomingCall);
    IncomingCallOverlayService.on('hideIncomingCall', handleHideIncomingCall);
    IncomingCallOverlayService.on('acceptCall', handleAcceptCall);
    IncomingCallOverlayService.on('rejectCall', handleRejectCall);

    return () => {
      // Cleanup timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Cleanup event listeners
      IncomingCallOverlayService.off('showIncomingCall', handleShowIncomingCall);
      IncomingCallOverlayService.off('hideIncomingCall', handleHideIncomingCall);
      IncomingCallOverlayService.off('acceptCall', handleAcceptCall);
      IncomingCallOverlayService.off('rejectCall', handleRejectCall);
    };
  }, []);

  const handleAccept = () => {
    // Clear timeout khi accept
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    IncomingCallOverlayService.acceptCall();
  };

  const handleReject = () => {
    // Clear timeout khi reject
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    IncomingCallOverlayService.rejectCall();
  };

  return (
    <>
      {children}
      
      {/* Incoming Call Modal */}
      <Modal
        visible={showNotification}
        transparent={true}
        animationType="none"
        statusBarTranslucent={true}
        onRequestClose={handleReject}
      >
        <IncomingCallNotification
          visible={showNotification}
          callerName={callData?.callerName || 'Người dùng'}
          callerAvatar={callData?.callerAvatar}
          onAccept={handleAccept}
          onReject={handleReject}
        />
      </Modal>
    </>
  );
};

export default IncomingCallProvider;

import React, { useEffect, useState } from 'react';
import { Modal } from 'react-native';
import IncomingCallNotification from './IncomingCallNotification';
import IncomingCallOverlayService from '../../services/IncomingCallOverlayService';
import WebRTCService from '../../features/call/WebRTCService';
import { navigate, RootScreen } from '../../router/router';

interface CallData {
  callerName: string;
  callerAvatar?: string;
  callerId: string;
}

const IncomingCallProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [showNotification, setShowNotification] = useState(false);
  const [callData, setCallData] = useState<CallData | null>(null);

  useEffect(() => {
    // Listen for incoming call events
    const handleShowIncomingCall = (data: CallData) => {
      setCallData(data);
      setShowNotification(true);
    };

    const handleHideIncomingCall = () => {
      setShowNotification(false);
      setCallData(null);
    };

    const handleAcceptCall = async (data: CallData) => {
      try {
        // Navigate to CallScreen
        navigate(RootScreen.CallScreen, {
          isIncoming: true,
          callerId: data.callerId,
          callerName: data.callerName,
          callerAvatar: data.callerAvatar,
        });

        // Accept call through WebRTC
        await WebRTCService.acceptCall();
      } catch (error) {
        console.error('Error accepting call:', error);
      }
    };

    const handleRejectCall = () => {
      WebRTCService.rejectCall();
    };

    // Register event listeners
    IncomingCallOverlayService.on('showIncomingCall', handleShowIncomingCall);
    IncomingCallOverlayService.on('hideIncomingCall', handleHideIncomingCall);
    IncomingCallOverlayService.on('acceptCall', handleAcceptCall);
    IncomingCallOverlayService.on('rejectCall', handleRejectCall);

    return () => {
      // Cleanup event listeners
      IncomingCallOverlayService.off('showIncomingCall', handleShowIncomingCall);
      IncomingCallOverlayService.off('hideIncomingCall', handleHideIncomingCall);
      IncomingCallOverlayService.off('acceptCall', handleAcceptCall);
      IncomingCallOverlayService.off('rejectCall', handleRejectCall);
    };
  }, []);

  const handleAccept = () => {
    IncomingCallOverlayService.acceptCall();
  };

  const handleReject = () => {
    IncomingCallOverlayService.rejectCall();
  };

  return (
    <>
      {children}
      
      {/* Incoming Call Modal */}
      <Modal
        visible={showNotification}
        transparent={true}
        animationType="none"
        statusBarTranslucent={true}
        onRequestClose={handleReject}
      >
        <IncomingCallNotification
          visible={showNotification}
          callerName={callData?.callerName || 'Người dùng'}
          callerAvatar={callData?.callerAvatar}
          onAccept={handleAccept}
          onReject={handleReject}
        />
      </Modal>
    </>
  );
};

export default IncomingCallProvider;

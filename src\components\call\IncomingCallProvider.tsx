import React, { useEffect, useState } from 'react';
import { Modal } from 'react-native';
import IncomingCallNotification from './IncomingCallNotification';
import IncomingCallOverlayService from '../../services/IncomingCallOverlayService';
import WebRTCService from '../../features/call/WebRTCService';
import { navigate, RootScreen } from '../../router/router';

interface CallData {
  callerName: string;
  callerAvatar?: string;
  callerId: string;
}

const IncomingCallProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [showNotification, setShowNotification] = useState(false);
  const [callData, setCallData] = useState<CallData | null>(null);
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Define functions outside useEffect để có thể access từ handlers
  const handleHideIncomingCall = React.useCallback(() => {
    // Clear timeout khi ẩn notification
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    setShowNotification(false);
    setCallData(null);
  }, []);

  const handleAcceptCall = React.useCallback(async (data: CallData) => {
    try {
      console.log('📞 Accepting call from:', data.callerName);

      // Ẩn notification trước
      handleHideIncomingCall();

      // Navigate to CallScreen
      navigate(RootScreen.CallScreen, {
        isIncoming: true,
        callerId: data.callerId,
        callerName: data.callerName,
        callerAvatar: data.callerAvatar,
      });

      // Accept call through WebRTC
      await WebRTCService.acceptCall();
    } catch (error) {
      console.error('Error accepting call:', error);
    }
  }, [handleHideIncomingCall]);

  const handleRejectCall = React.useCallback((data: CallData) => {
    console.log('📞 Rejecting call from:', data.callerName);

    // Ẩn notification trước
    handleHideIncomingCall();

    // Reject call through WebRTC
    WebRTCService.rejectCall();
  }, [handleHideIncomingCall]);

  useEffect(() => {
    // Listen for incoming call events
    const handleShowIncomingCall = (data: CallData) => {
      setCallData(data);
      setShowNotification(true);

      // Set timeout để tự động ẩn notification sau 60 giây
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        console.log('📞 Notification timeout, hiding notification');
        handleHideIncomingCall();
      }, 60000); // 60 giây
    };

    // Listen for WebRTC call state changes để auto-hide notification
    const handleCallStateChanged = (state: any) => {
      // Nếu cuộc gọi kết thúc (không còn trong call), ẩn notification
      if (!state.isInCall && showNotification) {
        console.log('📞 Call ended, hiding notification');
        handleHideIncomingCall();
      }
    };

    // Setup WebRTC callback để listen call state changes
    WebRTCService.setCallbacks({
      onCallStateChanged: handleCallStateChanged,
      onCallEnded: () => {
        console.log('📞 WebRTC call ended, hiding notification');
        handleHideIncomingCall();
      },
      onCallRejected: () => {
        console.log('📞 Call rejected, hiding notification');
        handleHideIncomingCall();
      },
      onError: (error) => {
        console.log('📞 WebRTC error, hiding notification:', error);
        handleHideIncomingCall();
      },
    });

    // Register event listeners
    IncomingCallOverlayService.on('showIncomingCall', handleShowIncomingCall);
    IncomingCallOverlayService.on('hideIncomingCall', handleHideIncomingCall);
    IncomingCallOverlayService.on('acceptCall', handleAcceptCall);
    IncomingCallOverlayService.on('rejectCall', handleRejectCall);

    return () => {
      // Cleanup timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Cleanup event listeners
      IncomingCallOverlayService.off('showIncomingCall', handleShowIncomingCall);
      IncomingCallOverlayService.off('hideIncomingCall', handleHideIncomingCall);
      IncomingCallOverlayService.off('acceptCall', handleAcceptCall);
      IncomingCallOverlayService.off('rejectCall', handleRejectCall);
    };
  }, [handleHideIncomingCall, handleAcceptCall, handleRejectCall, showNotification]);

  const handleAccept = () => {
    console.log('📞 User tapped Accept button, callData:', callData);
    if (callData) {
      // Clear timeout khi accept
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      // Gọi handleAcceptCall với data
      handleAcceptCall(callData);
    } else {
      console.error('📞 No callData available for accept');
    }
  };

  const handleReject = () => {
    console.log('📞 User tapped Reject button, callData:', callData);
    if (callData) {
      // Clear timeout khi reject
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      // Gọi handleRejectCall với data
      handleRejectCall(callData);
    } else {
      console.error('📞 No callData available for reject');
    }
  };

  return (
    <>
      {children}
      
      {/* Incoming Call Modal */}
      <Modal
        visible={showNotification}
        transparent={true}
        animationType="none"
        statusBarTranslucent={true}
        onRequestClose={handleReject}
      >
        <IncomingCallNotification
          visible={showNotification}
          callerName={callData?.callerName || 'Người dùng'}
          callerAvatar={callData?.callerAvatar}
          onAccept={handleAccept}
          onReject={handleReject}
        />
      </Modal>
    </>
  );
};

export default IncomingCallProvider;

import UIKit
import React
import React_RCTAppDelegate
import ReactAppDependencyProvider
import Firebase
import GoogleSignIn
import GoogleMaps
import VisionCamera
import react_native_webrtc

@main
class AppDelegate: RCTAppDelegate {
  override func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
    // Enable WebRTC multitasking camera access
    RTCInitializeSSL()
    RTCSetupInternalTracer()
    
    self.moduleName = "wini_core_mobile"
    self.dependencyProvider = RCTAppDependencyProvider()

    // You can add your custom initial props in the dictionary below.
    // They will be passed down to the ViewController used by React Native.
    self.initialProps = [:]

    GMSServices.provideAPIKey("AIzaSyDyq-nkW26hnSM__pUIoDFb2PfnirFzxgw")

    FirebaseApp.configure()

    // VisionCamera initialization is handled automatically by the module

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  override func bundleURL() -> URL? {
    #if DEBUG
      // In debug, always use the Metro server
      return RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index")
    #else
      // In release, use the bundled JS file
      return Bundle.main.url(forResource: "main", withExtension: "jsbundle") ?? RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index")
    #endif
  }
}



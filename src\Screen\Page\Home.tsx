/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Animated, RefreshControl, StyleSheet, View} from 'react-native';
import {FBottomSheet} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../router/router';
import {ColorThemes} from '../../assets/skin/colors';
import {HomeHeader} from '../Layout/headers/HomeHeader';
import {ScrollView} from 'react-native-gesture-handler';
import {DataController} from '../../base/baseController';
import CategoryGrid from '../../modules/category/CategoryGrid';
import ProductBestSeller from '../../modules/Product/productBestSeller';
import ByNewTrending from '../../modules/news/listview/byNews';
import HotProductsSection from '../../modules/Product/HotProductsSection';
import PointHome from '../../modules/wallet/pointHome';
import Missions from '../../modules/customer/listview/missions';
import NewsEventSection from '../../modules/news/section/NewsEventSection';
import BannerSection from '../../modules/Product/section/bannerSection';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';

const Home = () => {
  const navigation = useNavigation<any>();
  const bottomSheetRef = useRef<any>(null);
  const bannerDA = new DataController('Banner');
  const [refreshing, setRefreshing] = useState(false);

  const fadeAnim = useRef(new Animated.Value(0)).current; // Initial opacity value
  const customer = useSelectorCustomerState().data;
  // Function to start the fade-in animation
  const fadeIn = () => {
    // Reset opacity to 0
    fadeAnim.setValue(0);

    // Start the fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000, // 1 second fade-in
      useNativeDriver: true,
    }).start();
  };

  // State to trigger refreshes in child components
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleRefresh = () => {
    setRefreshing(true);

    // Trigger refresh in child components by updating the refreshTrigger
    // This will cause the useEffect to run and call getBanner()
    setRefreshTrigger(prev => prev + 1);
  };

  // Start fade animation when component mounts
  useEffect(() => {
    fadeIn();
  }, []);

  return (
    <View style={styles.container}>
      <FBottomSheet ref={bottomSheetRef} />
      <HomeHeader
        onSearchPress={() => {
          navigation.navigate(RootScreen.SearchIndex, {type: 'product&news'});
        }}
      />
      {/* Main content */}
      <ScrollView
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }>
        {/* Điểm thưởng */}
        {customer?.Id && <PointHome />}

        {/* Nhiệm vụ */}
        {customer?.Id && <Missions />}

        <View style={{paddingHorizontal: 16}}>
          {/* Danh mục */}
          <View style={styles.categorySection}>
            <CategoryGrid
              // categories={homeCategories}
              numColumns={3}
              onCategoryPress={category => {
                // Xử lý khi nhấn vào danh mục
                navigate(RootScreen.ProductListByCategory, {
                  categoryId: category.Id,
                  categoryName: category.Name,
                });
              }}
            />
          </View>
          {/* Sản phẩm HOT*/}
          <HotProductsSection
            title="Sản phẩm HOT"
            pageSize={10}
            onSeeAll={() =>
              navigation.navigate(RootScreen.ProductListByCategory)
            }
            onRefresh={refreshing}
          />

          {/* banner */}
          <BannerSection />

          {/* Sản phẩm bán chạy */}
          <ProductBestSeller
            horizontal={true}
            // titleList={'Sản phẩm nôi bật'}
            id="40caf5c67af14eb8b0c6de945d1d6f93"
            isSeeMore={true}
            onRefresh={refreshing}
            onPressSeeMore={() => {
              navigation.navigate(RootScreen.ProductListByCategory);
            }}
            key={`japan-advance-${refreshTrigger}`} // Force re-render on refresh
          />

          {/* sự kiện */}
          <NewsEventSection isRefresh={refreshing} />

          {/* tin tức */}
          <ByNewTrending
            id="40caf5c67af14eb8b0c6de945d1d6f22"
            horizontal={false}
            isSeeMore={true}
            onRefresh={() => {}}
            onPressSeeMore={() => {
              navigation.navigate(RootScreen.NewsScreen);
            }}
            titleList={'Tin tức'}
            key={`trending-${refreshTrigger}`} // Force re-render on refresh
          />
          <View style={{height: 100}} />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    width: '100%',
  },
  scrollContent: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    width: '100%',
    height: '100%',
    marginTop: 10,
  },
  categorySection: {
    backgroundColor: 'white',
    borderRadius: 8,
  },
  titleText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  textContainer: {
    paddingRight: 10,
  },
  mainText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
});

export default Home;

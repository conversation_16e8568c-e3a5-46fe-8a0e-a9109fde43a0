import React, {useState, useRef, useEffect} from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import {
  Winicon,
  FDialog,
  showDialog,
  ComponentStatus,
} from 'wini-mobile-components';
import {Title} from '../../../Config/Contanst';
import ConfigAPI from '../../../Config/ConfigAPI';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import {Ultis} from '../../../utils/Utils';
import PopupUpdateStatusOrder from '../../Popup/PopupUpdateStatusOrder';

const CardOrder = ({
  item,
  index,
  action,
  handleUpdateStatusProcessOrder,
  handleViewDetailOrder,
}: {
  item: any;
  index: number;
  action?: string;
  handleUpdateStatusProcessOrder: (item: any, type?: string) => void;
  handleViewDetailOrder: (item: any, refundInfo: any) => void;
}) => {
  const [showStatusPopup, setShowStatusPopup] = useState(false);
  const [showAllProducts, setShowAllProducts] = useState(false);
  const [refundInfo, setRefundInfo] = useState({
    all: 0,
    allRefund: 0,
    detail: [],
  });

  const dialogRef = useRef<any>(null);

  const handleStatusPress = () => {
    setShowStatusPopup(true);
  };

  const handleUpdate = async (itemWithCancelReason: any, status?: string) => {
    await handleUpdateStatusProcessOrder(itemWithCancelReason, status);
    setShowStatusPopup(false);
  };

  const handleToggleProducts = () => {
    setShowAllProducts(!showAllProducts);
  };

  // Lấy danh sách sản phẩm để hiển thị
  const getDisplayProducts = () => {
    if (!item?.orderDetails || item.orderDetails.length === 0) return [];
    return showAllProducts ? item.orderDetails : [item.orderDetails[0]];
  };

  const getTotalRefund = () => {
    return !item?.Refund
      ? 0
      : item.orderDetails?.reduce(
          (total: number, orderDetail: any) =>
            total +
            (JSON.parse(orderDetail?.Refund ?? '{}')?.f0 ?? 0) +
            (JSON.parse(orderDetail?.Refund ?? '{}')?.f1 ?? 0) +
            (JSON.parse(orderDetail?.Refund ?? '{}')?.f2 ?? 0),
          0,
        );
  };

  useEffect(() => {
    let totalRefund = 0;
    let refundCount = 0;
    const refundedProducts = new Set();

    if (item?.orderDetails) {
      item.orderDetails.forEach((orderDetail: any) => {
        if (orderDetail.historyReward) {
          const productHasRefund = orderDetail.historyReward.some(
            (history: any) => history.Value > 0,
          );

          if (productHasRefund) {
            refundedProducts.add(orderDetail.Id);
            orderDetail.historyReward.forEach((history: any) => {
              totalRefund += history.Value || 0;
            });
          }
        }
      });
    }

    refundCount = refundedProducts.size;

    const firstOrderDetail = item?.orderDetails;
    let arrayDetail: any[] = [];
    firstOrderDetail?.forEach((orderDetail: any) => {
      arrayDetail.push({
        id: orderDetail.Id,
        refundCustomer: orderDetail.historyReward?.find(
          (history: any) => history.Filial === 0,
        ),
        refundF1: orderDetail.historyReward?.find(
          (history: any) => history.Filial === 1,
        ),
        refundF2: orderDetail.historyReward?.find(
          (history: any) => history.Filial === 2,
        ),
      });
    });

    setRefundInfo({
      all: refundCount,
      allRefund: totalRefund,
      detail: arrayDetail as any,
    });
  }, [item]);

  return (
    <TouchableOpacity onPress={() => handleViewDetailOrder(item, refundInfo)}>
      <FDialog ref={dialogRef} />
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={{...styles.orderId, gap: 2, flexDirection: 'row'}}>
            {'Đơn hàng '}
            <Text style={{color: '#000', fontWeight: 'bold'}}>
              #{item?.Code}
            </Text>
          </Text>
          <Text
            style={
              item?.Status == 3
                ? styles.statusDone
                : item?.Status == 2 || item?.Status == 1
                ? styles.statusProcessing
                : styles.status
            }>
            {item?.Status == 1 && 'Chờ xác nhận'}
            {item?.Status == 2 && 'Đang thực hiện'}
            {item?.Status == 3 && 'Hoàn thành'}
            {item?.Status == 4 && 'Hủy'}
          </Text>
        </View>
        {/* Thông tin sản phẩm */}
        {getDisplayProducts().map((productItem: any, index: number) => (
          <View
            style={styles.productContainer}
            key={`${index}-${productItem?.Id}`}>
            <Image
              source={{uri: ConfigAPI.urlImg + productItem?.productInfo?.Img}}
              style={styles.productImage}
            />
            <View style={styles.productInfo}>
              <Text style={styles.productName}>
                {productItem?.productInfo?.Name}
              </Text>
              <Text style={styles.productDetails}>
                {
                  <>
                    <Text style={styles.productName}>Hoàn tiền :</Text>
                    <Text style={styles.productDetails}>
                      <Text>
                        (kh:{' '}
                        {Ultis.money(
                          productItem.historyReward?.find(
                            (history: any) => history.Filial === 0,
                          )?.Value ?? 0,
                        )}
                        đ)-(f1:
                        {Ultis.money(
                          productItem.historyReward?.find(
                            (history: any) => history.Filial === 1,
                          )?.Value ?? 0,
                        )}
                        đ)-(f2:
                        {Ultis.money(
                          productItem.historyReward?.find(
                            (history: any) => history.Filial === 2,
                          )?.Value ?? 0,
                        )}
                        đ)
                      </Text>
                    </Text>{' '}
                  </>
                }
              </Text>
              <Text style={styles.productPrice}>
                <Text style={styles.productName}>
                  Số lượng: {productItem?.Quantity}
                </Text>
              </Text>
              <Text style={styles.productPrice}>
                <Text style={styles.productName}>Giá:</Text>
                <Text style={{color: ColorThemes.light.error_main_color}}>
                  {Ultis.money(
                    (productItem?.Price ?? 0) *
                      (1 - (productItem?.Discount ?? 0) / 100),
                  )}{' '}
                  VNĐ
                </Text>
              </Text>
            </View>
          </View>
        ))}

        {/* Số lượng và tổng tiền */}
        <View style={styles.quantityTotal}>
          {item?.orderDetails?.length > 1 && (
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={handleToggleProducts}>
              <Text style={styles.quantityText}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {showAllProducts ? 'Thu gọn' : 'Xem thêm'}
                </Text>
                <Winicon
                  src={
                    showAllProducts
                      ? 'color/arrows/arrow-sm-up'
                      : 'color/arrows/arrow-sm-down'
                  }
                  size={13}
                  color={ColorThemes.light.neutral_text_title_color}
                />
              </Text>
            </TouchableOpacity>
          )}
          <View style={styles.quantityDetail}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                minWidth: 200,
              }}>
              <Text style={styles.quantity}>
                Tổng hoàn ({refundInfo.all} sản phẩm):
              </Text>
              <View style={{width: 30}}></View>
              <Text style={styles.money}>
                {Ultis.money(refundInfo.allRefund)} VNĐ
              </Text>
            </View>
          </View>
          <View style={styles.quantityDetail}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                minWidth: 200,
              }}>
              <Text style={styles.quantity}>
                Tổng tiền ({item?.orderDetails?.length ?? 0} sản phẩm):
              </Text>
              <View style={{width: 30}}></View>
              <Text style={styles.money}>{Ultis.money(item?.Value)} VNĐ</Text>
            </View>
          </View>
        </View>
        {action && action == 'Xác nhận đơn' && (
          <View style={styles.button}>
            <View style={{flexDirection: 'row', gap: 10}}></View>
            <View style={{flexDirection: 'row', gap: 10}}>
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={handleStatusPress}>
                <Text style={styles.confirmButtonText}>Xác nhận đơn hàng</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        {action &&
          action !== Title.Cancel &&
          action !== 'Xác nhận đơn' &&
          item?.Status !== 3 && (
            <View style={styles.button}>
              <View style={{flexDirection: 'row', gap: 10}}></View>
              <View style={{flexDirection: 'row', gap: 10}}>
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={handleStatusPress}>
                  <Text style={styles.confirmButtonText}>
                    Cập nhật trạng thái
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
      </View>
      <PopupUpdateStatusOrder
        visible={showStatusPopup}
        onClose={() => setShowStatusPopup(false)}
        item={item}
        handleUpdateStatusProcessOrder={handleUpdate}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
    padding: 10,
    margin: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2, // Bóng cho Android
    marginTop: 6,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  orderId: {
    flex: 0.6,
    ...TypoSkin.title2,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_title_color,
    fontSize: 14,
  },
  status: {
    flex: 0.4,
    textAlign: 'right',
    ...TypoSkin.title3,
    color: '#DA251D',
    fontSize: 14,
  },
  statusProcessing: {
    flex: 0.4,
    textAlign: 'right',
    ...TypoSkin.title3,
    color: '#FA8C16',
    fontSize: 14,
  },
  statusDone: {
    flex: 0.3,
    textAlign: 'right',
    ...TypoSkin.title3,
    color: ColorThemes.light.success_main_color,
    fontSize: 14,
  },
  productContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  productImage: {
    borderWidth: 5,
    borderRadius: 50,
    width: 60,
    height: 60,
    marginRight: 16,
    borderColor: '#F8F8FF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    ...TypoSkin.title4,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_title_color,
  },
  productDetails: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
    marginVertical: 2,
  },
  productPrice: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
  },
  quantityTotal: {
    flexDirection: 'column',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    marginBottom: 10,
  },
  quantityButton: {
    borderRadius: 5,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  quantityText: {
    fontSize: 14,
    color: '#555',
    display: 'flex',
    gap: 1,
  },
  quantityDetail: {
    height: 30,
    width: '100%',
    alignItems: 'flex-end',
  },
  quantity: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
  },
  money: {
    ...TypoSkin.title3,
    color: '#DA251D',
    fontWeight: 'bold',
    fontSize: 14,
  },
  button: {
    minHeight: 40,
    width: '100%',
    display: 'flex',
    alignItems: 'flex-end',
  },
  confirmButton: {
    backgroundColor: '#FFC043',
    height: 35,
    width: 154,
    borderRadius: 50,
    justifyContent: 'center',
  },
  confirmButtonText: {
    ...TypoSkin.title3,
    color: '#DA251D',
    fontWeight: 'bold',
    fontSize: 14,
    textAlign: 'center',
  },
});

export default CardOrder;

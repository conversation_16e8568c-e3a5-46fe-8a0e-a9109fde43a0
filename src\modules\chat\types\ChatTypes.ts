export interface ChatMessage {
  Id: string;                    // randomString
  DateCreated: number;           // Ngày tạo dạng gettime
  Content: string;               // Nội dung tin nhắn
  Type: number;                  // loại tin nhắn: 1-text, 2-ảnh, 3-File, 4-Emoji
  FileUrl?: string;              // dạng string để lưu các file ảnh, audio, file
  CustomerId: string;            // người gửi
  ChatRoomId: string;            // ID phòng chat

  // Additional fields for UI
  user?: ChatUser;               // Thông tin user (populated from CustomerId)
  sent?: boolean;                // Trạng thái gửi
  received?: boolean;            // Trạng thái nhận
  LastMessage?: string;          // Tin nhắn cuối cùng

  // Read receipt fields
  isRead?: boolean;              // Tin nhắn đã được đọc
  readAt?: number;               // Thời gian đọc
  readBy?: string[];             // Danh sách user đã đọc (cho group chat)
}

export interface ChatUser {
  Id: string | number;
  Name: string;
  Avatar?: string;
}

export interface QuickReplies {
  type: 'radio' | 'checkbox';
  values: QuickReply[];
  keepIt?: boolean;
}

export interface QuickReply {
  title: string;
  value: string;
  messageId?: any;
}

export interface ChatRoom {
  Id: string;
  id?: string; // For compatibility
  Name: string;
  name?: string; // For compatibility
  Avatar: string;
  CustomerId: string; // Comma-separated user IDs
  IsGroup: boolean;
  LastMessage: string;
  DateCreated: number;
  UpdatedAt: number;
  Members: string; // Comma-separated user IDs
}

export interface ChatState {
  rooms: ChatRoom[];
  currentRoom: ChatRoom | null;
  messages: { [roomId: string]: ChatMessage[] };
  loading: boolean;
  error: string | null;
  isConnected: boolean;
  userId?: string; // ID của user hiện tại đang kết nối socket
}

export interface CreateGroupRequest {
  name: string;
  participants: string[];
  avatar?: string;
}

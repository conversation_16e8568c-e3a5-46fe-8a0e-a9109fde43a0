import React, {useState} from 'react';
import {
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  View,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {ColorThemes} from '../../../assets/skin/colors';

interface Props {
  data: any[];
  onChangeTab: (tabId: string) => void;
}

const ScrollableTabs = ({data, onChangeTab}: Props) => {
  const [activeTabId, setActiveTabId] = useState(
    Array.isArray(data) && data.length > 0 ? data[0].id : '',
  );

  const handleTabPress = (tabId: string) => {
    try {
      if (!tabId || tabId === activeTabId) return;
      setActiveTabId(tabId);
      if (onChangeTab && typeof onChangeTab === 'function') {
        onChangeTab(tabId);
      }
    } catch (error) {
      console.error('Error in handleTabPress:', error);
    }
  };

  const renderTabItem = ({item}: {item: any}) => {
    const isActive = item.id === activeTabId;
    const TabContent = () => (
      <View style={styles.tabItem}>
        <FontAwesomeIcon
          icon={item.icon}
          size={16}
          color={isActive ? ColorThemes.light.primary_main_color : '#333'}
          style={styles.tabIcon}
        />
        <Text
          style={[
            styles.tabLabel,
            isActive && {color: ColorThemes.light.primary_main_color},
          ]}>
          {item.label}
        </Text>
      </View>
    );

    return (
      <TouchableOpacity
        style={[
          styles.touchableContainer,
          isActive && styles.activeTouchableContainer,
        ]}
        onPress={() => handleTabPress(item.id)}>
        {isActive ? (
          <LinearGradient
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            start={{x: 0, y: 0.5}}
            end={{x: 1, y: 0.5}}
            style={styles.gradientContainer}>
            <TabContent />
          </LinearGradient>
        ) : (
          <TabContent />
        )}
      </TouchableOpacity>
    );
  };

  // Don't render if data is invalid
  if (!Array.isArray(data) || data.length === 0) {
    return <View />;
  }

  return (
    <View>
      <FlatList
        data={data}
        renderItem={renderTabItem}
        keyExtractor={(item: any, index: number) =>
          item?.id ? `${item.id}_${index}` : `tab_${index}`
        }
        horizontal={true} // Quan trọng: để list cuộn ngang
        showsHorizontalScrollIndicator={false} // Ẩn thanh cuộn ngang cho giao diện sạch hơn
        removeClippedSubviews={Platform.OS === 'ios'}
        initialNumToRender={5}
        maxToRenderPerBatch={5}
        windowSize={10}
        getItemLayout={undefined} // Để FlatList tự tính toán
      />
    </View>
  );
};

const styles = StyleSheet.create({
  touchableContainer: {
    borderRadius: 10,
    overflow: 'hidden', // Quan trọng cho iOS để clip borderRadius
    // Thêm một số thuộc tính dành riêng cho iOS
    ...(Platform.OS === 'ios' && {
      shadowColor: 'transparent', // Tắt shadow mặc định
      shadowOffset: {width: 0, height: 0},
      shadowOpacity: 0,
      shadowRadius: 0,
    }),
  },
  activeTouchableContainer: {
    // Có thể thêm shadow hoặc elevation nếu cần
  },
  gradientContainer: {
    borderRadius: 10,
    overflow: 'hidden', // Đảm bảo gradient được clip đúng cách trên iOS
    // Đảm bảo gradient hiển thị đúng trên iOS
    ...(Platform.OS === 'ios' && {
      backgroundColor: 'transparent',
    }),
  },
  tabItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 10,
  },
  tabIcon: {
    marginRight: 8,
  },
  tabLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: ColorThemes.light.neutral_absolute_text_color,
  },
  activeTabLabel: {
    color: ColorThemes.light.primary_main_color,
  },
});

export default ScrollableTabs;

# Call System Fixes - Verification Guide

## ❌ **Vấn đề cần sửa:**

### **1. Accept Call Issues:**
- ❌ Không có timer đếm giây
- ❌ Không thể nói chuyện audio
- ❌ Không có mic và speaker controls
- ❌ <PERSON><PERSON><PERSON> hình stuck ở "bắt đầu cuộc gọi"

### **2. Reject Call Issues:**
- ❌ Người gọi không back về màn hình chính
- ❌ Stuck trong CallScreen

## ✅ **Fixes Applied:**

### **Fix 1: Timer Logic**
```typescript
// Simplified timer logic
if (!callTimerRef.current && state.isInCall) {
  if (state.isConnected || (!state.isIncoming && !state.isOutgoing)) {
    console.log('📞 Call connected/accepted, starting timer');
    startCallTimer();
  }
}
```

### **Fix 2: Controls Logic**
```typescript
// Show controls based on isIncoming state only
{callState.isIncoming ? (
  // Accept/Reject buttons
  <IncomingControls />
) : (
  // Mic/Speaker/End call buttons
  <CallControls />
)}
```

### **Fix 3: WebRTC State Management**
```typescript
// When accept call, set isIncoming = false to show controls
this.updateCallState({
  isIncoming: false, // Show in-call controls
  isConnected: true,
  callStartTime: new Date(),
});
```

### **Fix 4: Enhanced Debug Logging**
```typescript
// Track all state changes and navigation
console.log('📞 CallScreen received state change:', state);
console.log('📞 endCall() called, cleaning up timer and navigating back');
console.log('📞 User tapped OK on reject alert, calling endCall()');
```

## 🧪 **Test Scenarios:**

### **Test 1: Accept Incoming Call**
1. **Trigger**: User B receives call from User A
2. **Action**: User B taps Accept in FloatingIncomingCall
3. **Expected Results**:
   - ✅ Navigate to CallScreen
   - ✅ Show mic, speaker, end call buttons (NOT accept/reject)
   - ✅ Timer starts immediately (00:01, 00:02, 00:03...)
   - ✅ Status shows timer duration
   - ✅ Mic button toggles mute state
   - ✅ Speaker button toggles speaker state
   - ✅ End call button works

### **Test 2: Reject Incoming Call**
1. **Trigger**: User B receives call from User A
2. **Action**: User B taps Reject in FloatingIncomingCall
3. **Expected Results**:
   - ✅ User A sees "Cuộc gọi bị từ chối" alert
   - ✅ User A taps OK
   - ✅ User A navigates back to previous screen
   - ✅ No stuck CallScreen state

### **Test 3: Outgoing Call (Regression)**
1. **Trigger**: User A calls User B
2. **Action**: User B accepts
3. **Expected Results**:
   - ✅ User A timer starts when connected
   - ✅ Both users see proper controls
   - ✅ Call works normally

## 📱 **Console Logs to Watch:**

### **Accept Call Flow:**
```
📞 User tapped Accept button, callData: {...}
📞 Accepting call from: John Doe
📞 WebRTC State Update: {
  from: { isIncoming: true, isConnected: false },
  to: { isIncoming: false, isConnected: true },
  updates: { isIncoming: false, isConnected: true, callStartTime: ... }
}
📞 CallScreen received state change: { isIncoming: false, isConnected: true, ... }
📞 Call connected/accepted, starting timer
📞 Toggling mute: true/false
📞 Toggling speaker: true/false
```

### **Reject Call Flow:**
```
📞 User tapped Reject button, callData: {...}
📞 Rejecting call from: John Doe
📞 Call rejected by: user-123
📞 Call was rejected by receiver
📞 User tapped OK on reject alert, calling endCall()
📞 endCall() called, cleaning up timer and navigating back
📞 Timer cleared
📞 Navigating back...
```

## 🎯 **Success Criteria:**

### **✅ Accept Call:**
- **Timer**: Starts immediately và counts up (00:01, 00:02...)
- **Controls**: Shows mic, speaker, end call buttons
- **Audio**: Mic và speaker buttons toggle states
- **Status**: Shows call duration instead of "bắt đầu cuộc gọi"
- **UI**: Proper in-call interface

### **✅ Reject Call:**
- **Alert**: Caller sees reject alert
- **Navigation**: Caller navigates back when taps OK
- **Cleanup**: No stuck states
- **Ready**: System ready for next call

### **✅ General:**
- **Console logs**: Show proper state transitions
- **No errors**: No crashes or stuck states
- **Performance**: Smooth transitions
- **UX**: Professional call experience

## 🚨 **If Issues Persist:**

### **Debug Steps:**
1. **Check console logs** for state transitions
2. **Verify timer starts** with proper conditions
3. **Test controls** mic/speaker toggle
4. **Check navigation** back functionality

### **Common Issues:**
- **Timer not starting**: Check `isInCall` and `isConnected` states
- **Wrong controls**: Check `isIncoming` state after accept
- **Stuck navigation**: Check `endCall()` is called
- **Audio issues**: Check mic/speaker implementations

### **Manual Reset:**
```typescript
// If stuck, manually reset
WebRTCService.endCall();
navigation.goBack();
```

## 🔧 **Key Changes Summary:**

1. **Simplified timer logic**: Start when call is active and connected
2. **Fixed controls display**: Based on `isIncoming` state only
3. **Proper state management**: `isIncoming = false` after accept
4. **Enhanced debugging**: Comprehensive console logs
5. **Audio controls**: Mic and speaker toggle functionality
6. **Navigation cleanup**: Proper back navigation on reject

## 🎉 **Expected Results:**

After these fixes:
- ✅ **Accept call shows proper UI** with timer and controls
- ✅ **Timer counts up** immediately after accept
- ✅ **Mic and speaker buttons** work and toggle states
- ✅ **Reject call navigates back** properly
- ✅ **No stuck states** in any scenario
- ✅ **Professional call experience** like real phone apps

**Test these scenarios và check console logs để verify fixes!** 🎯

import {RefreshControl, ScrollView, StyleSheet, View} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';
import {RootScreen} from '../../router/router';
import {HomeHeader} from '../../Screen/Layout/headers/HomeHeader';
import {useNavigation} from '@react-navigation/native';
import {useEffect, useState, useCallback} from 'react';
import {useBanners} from './hooks/useBanners';
import BannerSection from '../Product/section/bannerSection';
import HotNewsSection from './section/HotNewsSection';
import LatestNewsSection from './section/LatestNewsSection';
import NewsEventSection from './section/NewsEventSection';

export default function NewsIndex() {
  const navigation = useNavigation<any>();
  const [refreshing, setRefreshing] = useState(false);
  const {banners, fetchBanners} = useBanners();

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchBanners();
    setRefreshing(false);
  }, [fetchBanners]);

  useEffect(() => {
    fetchBanners();
  }, [fetchBanners]);

  return (
    <View style={styles.container}>
      <HomeHeader
        onSearchPress={() => {
          navigation.navigate(RootScreen.SearchIndex, {type: 'news'});
        }}
      />
      <ScrollView
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }>
        {/* tin hot */}
        <View style={styles.sectionContainer}>
          <HotNewsSection isRefresh={refreshing} />
        </View>

        {/* tin sự kiện */}
        <View style={[styles.sectionContainer, {marginHorizontal: 16}]}>
          <NewsEventSection isRefresh={refreshing} />
        </View>

        {/* banner */}
        <View style={[styles.sectionContainer, {marginHorizontal: 16}]}>
          <BannerSection />
        </View>

        {/* tin mới */}
        <View style={styles.sectionContainer}>
          <LatestNewsSection isRefresh={refreshing} />
        </View>
        <View style={{height: 100}} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  scrollContent: {
    flex: 1,
    marginTop: 10,
  },
  sectionContainer: {
    marginVertical: 12,
  },
});

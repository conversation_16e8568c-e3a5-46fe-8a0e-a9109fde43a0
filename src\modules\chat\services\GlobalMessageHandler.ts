import { Dispatch } from '@reduxjs/toolkit';
import SocketService from './SocketService';
import Cha<PERSON><PERSON><PERSON> from './ChatAPI';
import { ChatMessage, ChatRoom } from '../types/ChatTypes';
import { addMessage, addChatRoom, updateChatRoom } from '../../../redux/reducers/ChatReducer';
import { navigate, RootScreen } from '../../../router/router';
import { showSnackbar, ComponentStatus } from 'wini-mobile-components';
import { navigationRef } from '../../../router/router';
import { DataController } from '../../../base/baseController';

class GlobalMessageHandler {
  private dispatch: Dispatch | null = null;
  private currentUserId: string | null = null;
  private isInitialized: boolean = false;

  /**
   * Initialize global message handler
   */
  initialize(dispatch: Dispatch, userId: string) {
    this.dispatch = dispatch;
    this.currentUserId = userId;    
    if (!this.isInitialized) {
      this.setupGlobalMessageListener();
      this.isInitialized = true;
    }
  }

  /**
   * Setup global message listener for messages received outside current room
   */
  private setupGlobalMessageListener() {
    SocketService.onReceiveMessage(async (data) => {
      try {
        const { roomId, fromUserId, message } = data;
        
        // Skip if message is from current user
        if (fromUserId === this.currentUserId) {
          return;
        }

        // Check if user is currently in this room
        const currentRoute = navigationRef.current?.getCurrentRoute() as any;
        const isInCurrentRoom = currentRoute?.name === 'ChatRoom' && 
                               currentRoute?.params?.room?.Id === roomId;

        if (!isInCurrentRoom) {
          // User is not in this room, handle as new message notification
          await this.handleNewMessageOutsideRoom(roomId, fromUserId, message);
        }
      } catch (error) {
        console.error('Error handling global message:', error);
      }
    });
  }
  /**
   * Handle new message when user is outside the room
   */
  private async handleNewMessageOutsideRoom(roomId: string, fromUserId: string, message: any) {
    try {
      // Format message
      const chatMessage: ChatMessage = {
        Id: message.Id,
        Content: message.Content || message.text || message,
        DateCreated: message.DateCreated || new Date().getTime(),
        Type: message.Type || 1,
        FileUrl: message.FileUrl,
        CustomerId: fromUserId,
        ChatRoomId: roomId,
        user: {
          Id: fromUserId,
          Name: message.user?.Name || 'User',
          Avatar: message.user?.Avatar,
        },
        received: true,
        isRead: false,
      };

      // Add message to Redux store
      if (this.dispatch) {
        this.dispatch(addMessage({ roomId, message: chatMessage }));
      }
      // Check if room exists in current room list
      const chatcontroller = new DataController('ChatRoom');
      const existingRoom = await chatcontroller.getById(roomId);      
      if (existingRoom) {        // Update existing room with new message
        const updatedRoom: ChatRoom = {
          ...existingRoom,
          LastMessage: message.Type === 2 ? 'Hình ảnh' : message.Type === 3 ? 'File đính kèm' : message.Content,
          UpdatedAt: new Date().getTime(),
        };

        if (this.dispatch) {
          this.dispatch(updateChatRoom(updatedRoom));
        }
      }

      // Show notification
      this.showMessageNotification(chatMessage, existingRoom);

    } catch (error) {
      console.error('Error handling new message outside room:', error);
    }
  }


  /**
   * Show in-app notification for new message
   */
  private showMessageNotification(message: ChatMessage, room: ChatRoom | null) {
    const senderName = message.user?.Name || 'Someone';
    const roomName = room?.Name || senderName;
    const messagePreview = message.Type === 2 ? '📷 Hình ảnh' : 
                          message.Type === 3 ? '📎 File đính kèm' : 
                          message.Content;

    showSnackbar({
      status: ComponentStatus.INFOR,
      message: `${senderName}: ${messagePreview}`,
      actionTitle: 'Xem',
      action: ()=>{navigate(RootScreen.ChatRoom, { room: room}); return true;},      
    });
  }

  /**
   * Cleanup
   */
  cleanup() {
    this.dispatch = null;
    this.currentUserId = null;
    this.isInitialized = false;
  }
}

export default new GlobalMessageHandler();

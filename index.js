import 'react-native-reanimated';
/**
 * @format
 */

import { AppRegistry, Platform, Text, TextInput, I18nManager } from 'react-native';
import App from './App';
import { name as appName } from './app.json';

import 'react-native-get-random-values'

// Đặt defaultProps trước khi đăng ký component
// Đảm bảo thiết lập này được thực hiện trước khi render bất kỳ component nào
Text.defaultProps = {
    ...(Text.defaultProps || {}),
    allowFontScaling: false,
    Color: '#111010FF',
};

TextInput.defaultProps = {
    ...(TextInput.defaultProps || {}),
    allowFontScaling: false,
};

// Force LTR layout
I18nManager.allowRTL(false);
I18nManager.forceRTL(false);

AppRegistry.registerComponent(appName, () => App);

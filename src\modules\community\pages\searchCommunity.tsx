import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {AppSvg, FBottomSheet, FDialog, TextField} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';

// Internal imports
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import HeaderLogo from '../../../Screen/Layout/headers/HeaderLogo';
import iconSvg from '../../../svg/icon';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import {DataController} from '../../../base/baseController';
import {DefaultPost} from '../card/defaultPost';
import {Ultis} from '../../../utils/Utils';
import ConfigAPI from '../../../Config/ConfigAPI';

const {width} = Dimensions.get('window');

// TypeScript interfaces
interface CommunityPost {
  Id: string;
  Content?: string;
  Name?: string;
  CustomerId: string;
  DateCreated: number;
  CustomerName?: string;
  CustomerAvatar?: string;
  Img?: string;
  LinkVideo?: string;
  IsHidden?: boolean;
}

// Constants
const SEARCH_HISTORY_KEY = 'search_history_community';
const RECENT_POSTS_KEY = 'recent_posts_community';
const MAX_HISTORY_ITEMS = 5;
const MAX_RECENT_POSTS = 6;

export const SearchCommunity = () => {
  // State management
  const [searchValue, setSearchValue] = useState<string>('');
  const [communityPosts, setCommunityPosts] = useState<CommunityPost[]>([]);
  const [isRefresh, setRefresh] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [recentPosts, setRecentPosts] = useState<CommunityPost[]>([]);

  // Hooks
  const navigation = useNavigation<any>();

  // Refs
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const textInputRef = useRef<any>(null);

  // Data controller - only postController for community
  const postController = new DataController('Post');

  // Load search history and recent posts when component mounts
  useEffect(() => {
    loadSearchHistory();
    loadRecentPosts();
  }, []);

  const loadSearchHistory = async (): Promise<void> => {
    try {
      const history = await getDataToAsyncStorage(SEARCH_HISTORY_KEY);
      if (history !== null) {
        setSearchHistory(JSON.parse(history));
      }
    } catch (error) {
      console.error('Error loading search history:', error);
    }
  };

  const loadRecentPosts = async (): Promise<void> => {
    try {
      const posts = await getDataToAsyncStorage(RECENT_POSTS_KEY);
      if (posts !== null) {
        setRecentPosts(JSON.parse(posts));
      }
    } catch (error) {
      console.error('Error loading recent posts:', error);
    }
  };

  const saveSearchHistory = async (newHistory: string[]): Promise<void> => {
    try {
      await saveDataToAsyncStorage(
        SEARCH_HISTORY_KEY,
        JSON.stringify(newHistory),
      );
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  };

  const saveRecentPosts = async (newPosts: CommunityPost[]): Promise<void> => {
    try {
      await saveDataToAsyncStorage(RECENT_POSTS_KEY, JSON.stringify(newPosts));
    } catch (error) {
      console.error('Error saving recent posts:', error);
    }
  };

  const addToSearchHistory = async (searchTerm: string) => {
    const trimmedTerm = searchTerm.trim();

    if (trimmedTerm && !searchHistory.includes(trimmedTerm)) {
      let newHistory = [trimmedTerm, ...searchHistory];

      // Limit to MAX_HISTORY_ITEMS
      if (newHistory.length > MAX_HISTORY_ITEMS) {
        newHistory = newHistory.slice(0, MAX_HISTORY_ITEMS);
      }

      setSearchHistory(newHistory);
      await saveSearchHistory(newHistory);
    }
  };

  const addToRecentPosts = async (posts: CommunityPost[]): Promise<void> => {
    if (posts && posts.length > 0) {
      // Take first few items from search results
      const postsToAdd = posts.slice(0, 3);

      // Remove duplicates and add new posts to the beginning
      let newRecentPosts = [...postsToAdd];

      // Add existing posts that are not duplicates
      recentPosts.forEach((existingPost: CommunityPost) => {
        const isDuplicate = newRecentPosts.some(
          (newPost: CommunityPost) => newPost.Id === existingPost.Id,
        );
        if (!isDuplicate && newRecentPosts.length < MAX_RECENT_POSTS) {
          newRecentPosts.push(existingPost);
        }
      });

      // Limit to MAX_RECENT_POSTS
      if (newRecentPosts.length > MAX_RECENT_POSTS) {
        newRecentPosts = newRecentPosts.slice(0, MAX_RECENT_POSTS);
      }

      setRecentPosts(newRecentPosts);
      await saveRecentPosts(newRecentPosts);
    }
  };

  // Community search function
  const performSearch = useCallback(
    async (searchText: string, pageNumber: number = 1): Promise<void> => {
      if (!searchText.trim()) {
        setCommunityPosts([]);
        return;
      }

      try {
        if (pageNumber === 1) {
          isRefresh ? setRefresh(true) : setIsLoading(true);
        }

        // Search community posts with customer data
        const postResult = await postController.getPatternList({
          page: pageNumber,
          size: 20,
          query: `@Content: (*${searchText.trim()}*) | @Name: (*${searchText.trim()}*)`,
          pattern: {
            CustomerId: ['Id', 'Name', 'AvatarUrl'],
          },
        });

        if (postResult?.code === 200) {
          // Enrich posts with customer data
          const posts = postResult.data || [];
          const customers = postResult.Customer || [];

          const enrichedPosts: CommunityPost[] = posts.map((post: any) => {
            const customer = customers.find(
              (c: any) => c.Id === post.CustomerId,
            );
            return {
              ...post,
              CustomerName: customer?.Name || 'Unknown User',
              CustomerAvatar: customer?.AvatarUrl
                ? `${ConfigAPI.urlImg}${customer.AvatarUrl}`
                : '',
            };
          });

          if (pageNumber === 1) {
            setCommunityPosts(enrichedPosts);
            // Add to search history and recent posts
            if (enrichedPosts.length > 0) {
              await addToSearchHistory(searchText);
              await addToRecentPosts(enrichedPosts);
            }
          } else {
            setCommunityPosts(prev => [...prev, ...enrichedPosts]);
          }
        }
      } catch (err) {
        console.error('Error performing community search:', err);
        if (pageNumber === 1) {
          setCommunityPosts([]);
        }
      } finally {
        setIsLoading(false);
        setRefresh(false);
      }
    },
    [postController],
  );

  const handleHistoryItemPress = async (term: string): Promise<void> => {
    setSearchValue(term);
    // Trigger search immediately
    await performSearch(term);
  };

  // Refresh function
  const onRefresh = async (): Promise<void> => {
    await performSearch(searchValue);
  };

  return (
    <View
      style={{
        width: '100%',
        height: Dimensions.get('window').height,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      <HeaderLogo isShowBack />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          flex: 1,
          paddingHorizontal: 16,
          gap: 8,
          marginBottom: 32,
          zIndex: 999,
        }}>
        <TextField
          ref={textInputRef}
          style={{
            paddingHorizontal: 16,
            width: '100%',
            height: 36,
            paddingBottom: 4,
          }}
          onChange={async (vl: string) => {
            setSearchValue(vl.trim());
            await performSearch(vl.trim());
          }}
          value={searchValue}
          placeholder="Bạn muốn tìm gì?"
        />
        {/* <AppSvg
          SvgSrc={iconSvg.filter}
          color="#000"
          size={25}
          style={{marginRight: 12}}
        /> */}
      </View>
      <ScrollView
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: '#F1F1F1FF',
        }}
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        contentContainerStyle={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          paddingHorizontal: 16,
          paddingTop: 16,
        }}>
        {/* Loading State */}
        {isLoading && (
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
              paddingVertical: 50,
            }}>
            <ActivityIndicator color={ColorThemes.light.Primary_Color_Main} />
          </View>
        )}

        {/* Empty State - No search results */}
        {!isLoading &&
          searchValue.trim().length > 0 &&
          communityPosts.length === 0 && (
            <EmptyPage
              title="Không tìm thấy bài viết"
              subtitle={`Không có bài viết nào cho "${searchValue}"`}
            />
          )}

        {/* Empty State - No search term */}
        {!isLoading && searchValue.trim().length === 0 && (
          <EmptySearchWithHistory
            histories={searchHistory}
            recentPosts={recentPosts}
            onHistoryPress={handleHistoryItemPress}
            onItemPress={(post: CommunityPost) => {
              navigation.navigate(RootScreen.PostDetail, {
                item: {
                  ...post,
                  relativeUser: {
                    image: post.CustomerAvatar || '',
                    title: post.CustomerName || 'Unknown User',
                    subtitle: Ultis.getDiffrentTime(post.DateCreated),
                  },
                },
              });
            }}
          />
        )}

        {/* Community Posts Results */}
        {!isLoading && communityPosts.length > 0 && (
          <FlatList
            data={communityPosts}
            scrollEnabled={false}
            keyExtractor={item => `community-${item.Id}`}
            style={{
              width: '100%',
            }}
            contentContainerStyle={{gap: 16}}
            renderItem={({item}) => (
              <DefaultPost
                data={{
                  ...item,
                  relativeUser: {
                    image: item.CustomerAvatar || '',
                    title: item.CustomerName || 'Unknown User',
                    subtitle: Ultis.getDiffrentTime(item.DateCreated),
                  },
                }}
                containerStyle={{borderRadius: 8}}
                onPressDetail={() => {
                  navigation.navigate(RootScreen.PostDetail, {
                    item: {
                      ...item,
                      relativeUser: {
                        image: item.CustomerAvatar || '',
                        title: item.CustomerName || 'Unknown User',
                        subtitle: Ultis.getDiffrentTime(item.DateCreated),
                      },
                    },
                  });
                }}
                showContent={true}
              />
            )}
          />
        )}
      </ScrollView>
    </View>
  );
};

interface EmptySearchWithHistoryProps {
  histories: string[];
  recentPosts: CommunityPost[];
  onHistoryPress: (term: string) => Promise<void>;
  onItemPress: (post: CommunityPost) => void;
}

const EmptySearchWithHistory = ({
  histories,
  recentPosts,
  onHistoryPress,
  onItemPress,
}: EmptySearchWithHistoryProps) => {
  return (
    <View style={{height: '100%', width: '100%'}}>
      {/* History Section */}
      {histories && histories.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Lịch sử tìm kiếm</Text>
          <View style={styles.categoryContainer}>
            {histories.map((term: string, index: number) => (
              <TouchableOpacity
                key={index}
                style={styles.categoryButton}
                onPress={() => onHistoryPress(term)}>
                <Text style={styles.categoryText}>{term}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Recent Community Posts Section */}
      {recentPosts && recentPosts.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Bài viết gần đây</Text>
          <View style={{flexDirection: 'column'}}>
            {recentPosts.map((post: CommunityPost) => (
              <DefaultPost
                key={post.Id}
                data={{
                  ...post,
                  relativeUser: {
                    image: post.CustomerAvatar || '',
                    title: post.CustomerName || 'Unknown User',
                    subtitle: Ultis.getDiffrentTime(post.DateCreated),
                  },
                }}
                containerStyle={{marginBottom: 16, borderRadius: 8}}
                onPressDetail={() => onItemPress(post)}
                showContent={true}
              />
            ))}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  categoryButton: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    marginBottom: 10,
  },
  categoryText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
});

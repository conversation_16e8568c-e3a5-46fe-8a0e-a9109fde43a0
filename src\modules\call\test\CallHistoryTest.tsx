import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import CallHistoryAPI from '../services/CallHistoryAPI';
import { CallHistory } from '../types/CallTypes';

// Component test để kiểm tra CallHistoryAPI
const CallHistoryTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testCreateCallHistory = async () => {
    try {
      addResult('🧪 Testing createCallHistory...');
      
      const result = await CallHistoryAPI.createCallHistory({
        Name: 'Test User',
        Receiver: 'test-receiver-id',
        CallerId: 'test-caller-id',
        CallerName: 'Test Caller',
      });

      if (result) {
        addResult(`✅ CallHistory created: ${result.Id}`);
        return result.Id;
      } else {
        addResult('❌ Failed to create CallHistory');
        return null;
      }
    } catch (error: any) {
      addResult(`❌ Error creating CallHistory: ${error.message}`);
      return null;
    }
  };

  const testUpdateCallHistory = async (callId: string) => {
    try {
      addResult('🧪 Testing updateCallHistory...');
      
      // Test update IsAccept
      const result1 = await CallHistoryAPI.updateCallHistory({
        Id: callId,
        IsAccept: true,
      });

      if (result1) {
        addResult('✅ CallHistory updated: IsAccept = true');
      } else {
        addResult('❌ Failed to update IsAccept');
      }

      // Test update Time and EndReason
      const result2 = await CallHistoryAPI.updateCallHistory({
        Id: callId,
        Time: 120, // 2 minutes
        EndReason: 'completed',
      });

      if (result2) {
        addResult('✅ CallHistory updated: Time = 120s, EndReason = completed');
      } else {
        addResult('❌ Failed to update Time and EndReason');
      }

      return result1 && result2;
    } catch (error: any) {
      addResult(`❌ Error updating CallHistory: ${error.message}`);
      return false;
    }
  };

  const testGetCallHistory = async (userId: string) => {
    try {
      addResult('🧪 Testing getCallHistory...');
      
      const result = await CallHistoryAPI.getCallHistory(userId);
      
      addResult(`✅ Retrieved ${result.data.length} call history records`);
      
      if (result.data.length > 0) {
        const latest = result.data[0];
        addResult(`📋 Latest call: ${latest.Name} - ${latest.IsAccept ? 'Accepted' : 'Not accepted'}`);
      }

      return result.data;
    } catch (error: any) {
      addResult(`❌ Error getting CallHistory: ${error.message}`);
      return [];
    }
  };

  const testGetStatistics = async (userId: string) => {
    try {
      addResult('🧪 Testing getCallStatistics...');
      
      const stats = await CallHistoryAPI.getCallStatistics(userId);
      
      addResult(`📊 Statistics:`);
      addResult(`   Total calls: ${stats.totalCalls}`);
      addResult(`   Accepted calls: ${stats.acceptedCalls}`);
      addResult(`   Missed calls: ${stats.missedCalls}`);
      addResult(`   Total duration: ${stats.totalDuration}s`);
      addResult(`   Average duration: ${Math.round(stats.averageDuration)}s`);

      return stats;
    } catch (error: any) {
      addResult(`❌ Error getting statistics: ${error.message}`);
      return null;
    }
  };

  const runFullTest = async () => {
    setLoading(true);
    clearResults();
    
    try {
      addResult('🚀 Starting CallHistory API tests...');
      
      // Test 1: Create CallHistory
      const callId = await testCreateCallHistory();
      if (!callId) {
        addResult('❌ Cannot continue tests without valid call ID');
        return;
      }

      // Test 2: Update CallHistory
      await testUpdateCallHistory(callId);

      // Test 3: Get CallHistory
      const testUserId = 'test-caller-id'; // Use the same ID from create test
      await testGetCallHistory(testUserId);

      // Test 4: Get Statistics
      await testGetStatistics(testUserId);

      addResult('🎉 All tests completed!');
      
    } catch (error: any) {
      addResult(`❌ Test suite failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testWithRealUser = () => {
    Alert.prompt(
      'Test with Real User',
      'Enter your User ID to test with real data:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Test',
          onPress: async (userId) => {
            if (userId) {
              setLoading(true);
              clearResults();
              addResult(`🧪 Testing with real user: ${userId}`);
              await testGetCallHistory(userId);
              await testGetStatistics(userId);
              setLoading(false);
            }
          },
        },
      ],
      'plain-text'
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>CallHistory API Test</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.testButton]}
          onPress={runFullTest}
          disabled={loading}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Running Tests...' : 'Run Full Test Suite'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.realUserButton]}
          onPress={testWithRealUser}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test with Real User</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  testButton: {
    backgroundColor: '#4CAF50',
  },
  realUserButton: {
    backgroundColor: '#2196F3',
  },
  clearButton: {
    backgroundColor: '#FF9800',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  resultText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 2,
    lineHeight: 16,
  },
});

export default CallHistoryTest;

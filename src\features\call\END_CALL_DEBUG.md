# End Call Button Debug

## 🐛 **Problem:**
End call button không ấn được sau khi accept call và timer đã chạy.

## 🔧 **Fixes Applied:**

### **1. Enhanced handleEndCall:**
```typescript
const handleEndCall = () => {
  console.log('📞 handleEndCall called');
  try {
    WebRTCService.endCall();
    console.log('📞 WebRTC endCall completed');
  } catch (error) {
    console.error('📞 Error ending call:', error);
  }
  
  // Force cleanup và navigate (không chờ WebRTC callback)
  console.log('📞 Force cleanup and navigate');
  endCall();
};
```

### **2. Enhanced Button Debug:**
```typescript
<TouchableOpacity
  style={[
    styles.controlButton, 
    styles.endCallButton,
    { borderWidth: 2, borderColor: 'yellow' } // Debug border
  ]}
  onPress={() => {
    console.log('📞 End call button pressed');
    handleEndCall();
  }}
  onPressIn={() => console.log('📞 End call button press IN')}
  onPressOut={() => console.log('📞 End call button press OUT')}
>
```

### **3. Test Button:**
```typescript
<TouchableOpacity
  style={{ backgroundColor: 'blue', padding: 10, marginBottom: 10 }}
  onPress={() => {
    console.log('📞 TEST BUTTON PRESSED');
    handleEndCall();
  }}
>
  <Text>TEST END CALL</Text>
</TouchableOpacity>
```

## 🧪 **Test Steps:**

### **Step 1: Visual Check**
- ✅ End call button có yellow border (debug)
- ✅ Test button màu xanh hiển thị
- ✅ Buttons có proper size và position

### **Step 2: Touch Test**
1. **Tap test button** (blue) → should work
2. **Tap end call button** (red with yellow border) → check if works
3. **Watch console logs** for press events

### **Expected Console Logs:**
```
📞 End call button press IN
📞 End call button pressed
📞 handleEndCall called
📞 WebRTC endCall completed
📞 Force cleanup and navigate
📞 endCall() called, cleaning up timer and navigating back
📞 Timer cleared
📞 Navigating back...
```

## 🚨 **If Button Still Not Working:**

### **Possible Issues:**

#### **1. Layout Overlap:**
- Another element covering the button
- Z-index issues
- Absolute positioning conflicts

#### **2. Touch Area:**
- Button too small
- Touch area blocked
- Parent view blocking touches

#### **3. State Issues:**
- Button disabled by state
- Component re-rendering issues
- Event handler not bound

### **Debug Solutions:**

#### **1. Increase Button Size:**
```typescript
endCallButton: {
  backgroundColor: '#F44336',
  transform: [{ rotate: '135deg' }],
  width: 100, // Increase size
  height: 100,
  borderRadius: 50,
},
```

#### **2. Remove Transform:**
```typescript
endCallButton: {
  backgroundColor: '#F44336',
  // transform: [{ rotate: '135deg' }], // Remove rotation
},
```

#### **3. Simplify onPress:**
```typescript
onPress={() => {
  console.log('📞 SIMPLE END CALL');
  navigation.goBack();
}}
```

#### **4. Check Parent Container:**
```typescript
// Add to callControls style
callControls: {
  flexDirection: 'row',
  justifyContent: 'space-around',
  alignItems: 'center',
  pointerEvents: 'auto', // Ensure touches work
  zIndex: 1000,
},
```

## 🎯 **Success Criteria:**

### **✅ Button Works:**
- Console shows press IN/OUT events
- handleEndCall() gets called
- Timer stops và navigation works

### **✅ Visual Feedback:**
- Button has yellow debug border
- Button responds to touch (opacity change)
- No layout issues

### **✅ Functionality:**
- Call ends properly
- Timer stops
- Navigate back to previous screen
- WebRTC cleanup happens

## 🔧 **Quick Fixes to Try:**

### **1. Test Button First:**
- If blue test button works → layout issue with red button
- If test button doesn't work → general touch issue

### **2. Remove Rotation:**
```typescript
// Temporarily remove transform
endCallButton: {
  backgroundColor: '#F44336',
  // transform: [{ rotate: '135deg' }],
},
```

### **3. Force Navigation:**
```typescript
// Simplest possible end call
onPress={() => {
  console.log('📞 FORCE END');
  navigation.goBack();
}}
```

### **4. Check Button Position:**
- Look for yellow border around button
- Verify button is visible và not covered
- Check if button size is adequate

## 📱 **Test Flow:**

1. **Accept call** → Timer starts, controls show
2. **Look for yellow border** around end call button
3. **Tap test button** → should work
4. **Tap end call button** → check console logs
5. **Verify navigation** back to previous screen

**Try test button first để xem có phải layout issue không!** 🔍

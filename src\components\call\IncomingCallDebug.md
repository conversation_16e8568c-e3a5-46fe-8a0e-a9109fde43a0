# IncomingCallNotification Debug Guide

## ✅ **<PERSON><PERSON> sửa các vấn đề:**

### **1. <PERSON><PERSON><PERSON> <PERSON> bị vỡ - FIXED**
**Vấn đề**: Buttons nhảy lung tung, layout không ổn định
**G<PERSON><PERSON><PERSON> pháp**:
```typescript
// Container height từ cố định 200px → full height với overlay
height: '100%',

// Notification card với minHeight
minHeight: 140,

// Action container với spacing đều
justifyContent: 'space-evenly',
paddingHorizontal: 20,

// Button size tăng lên
width: 60, height: 60,

// Loại bỏ transform rotate gây lỗi layout
// transform: [{ rotate: '135deg' }], // REMOVED
```

### **2. Action accept/reject không hoạt động - FIXED**
**Vấn đề**: Buttons không trigger WebRTC actions
**Giải pháp**:
```typescript
// Move functions ra ngoài useEffect với useCallback
const handleAcceptCall = React.useCallback(async (data: CallData) => {
  handleHideIncomingCall(); // Ẩn notification trước
  navigate(RootScreen.CallScreen, { ... }); // Navigate
  await WebRTCService.acceptCall(); // WebRTC action
}, [handleHideIncomingCall]);

// Button handlers pass đúng data
const handleAccept = () => {
  if (callData) {
    handleAcceptCall(callData); // Pass callData
  }
};
```

### **3. Notification không tự ẩn - FIXED**
**Vấn đề**: Khi caller end call, notification vẫn hiển thị
**Giải pháp**:
```typescript
// Multiple auto-hide mechanisms
WebRTCService.setCallbacks({
  onCallStateChanged: (state) => {
    if (!state.isInCall) handleHideIncomingCall();
  },
  onCallEnded: () => handleHideIncomingCall(),
  onCallRejected: () => handleHideIncomingCall(),
  onError: () => handleHideIncomingCall(), // NEW
});

// Timeout auto-hide
setTimeout(() => handleHideIncomingCall(), 60000);

// Manual action auto-hide
handleAcceptCall() { handleHideIncomingCall(); ... }
handleRejectCall() { handleHideIncomingCall(); ... }
```

## 🔧 **Debug Steps:**

### **Step 1: Check Notification Display**
```typescript
// Console logs để check
console.log('📞 Showing notification for:', callerName);
console.log('📞 Notification visible:', showNotification);
console.log('📞 Call data:', callData);
```

### **Step 2: Check Button Actions**
```typescript
// Console logs trong button handlers
console.log('📞 User tapped Accept button, callData:', callData);
console.log('📞 User tapped Reject button, callData:', callData);
```

### **Step 3: Check Auto-Hide**
```typescript
// Console logs cho auto-hide events
console.log('📞 WebRTC call ended, hiding notification');
console.log('📞 Call rejected, hiding notification');
console.log('📞 Notification timeout, hiding notification');
```

### **Step 4: Check WebRTC Integration**
```typescript
// Check WebRTC callbacks
WebRTCService.setCallbacks({
  onCallStateChanged: (state) => {
    console.log('📞 WebRTC state changed:', state);
  },
  onCallEnded: () => {
    console.log('📞 WebRTC onCallEnded triggered');
  },
});
```

## 🎯 **Testing Checklist:**

### **UI Testing:**
- [ ] Notification slides down from top smoothly
- [ ] Card layout is stable and not broken
- [ ] Avatar displays correctly
- [ ] Caller name and subtitle show properly
- [ ] Buttons are properly positioned and sized
- [ ] No layout jumping or shifting

### **Functionality Testing:**
- [ ] Accept button navigates to CallScreen
- [ ] Accept button calls WebRTCService.acceptCall()
- [ ] Reject button calls WebRTCService.rejectCall()
- [ ] Both buttons hide notification immediately
- [ ] Console logs show proper data flow

### **Auto-Hide Testing:**
- [ ] 60-second timeout hides notification
- [ ] Caller ending call hides notification
- [ ] WebRTC state changes hide notification
- [ ] Error conditions hide notification
- [ ] No stuck notifications in any scenario

## 🚨 **Common Issues & Solutions:**

### **Issue 1: Buttons not working**
```typescript
// Check if callData is available
if (!callData) {
  console.error('📞 No callData available');
  return;
}

// Check if functions are properly bound
const handleAccept = useCallback(() => {
  // ... implementation
}, [callData, handleAcceptCall]);
```

### **Issue 2: Notification not hiding**
```typescript
// Check if WebRTC callbacks are set
useEffect(() => {
  WebRTCService.setCallbacks({
    onCallEnded: () => {
      console.log('📞 Force hiding notification');
      setShowNotification(false);
    },
  });
}, []);
```

### **Issue 3: Layout breaking**
```typescript
// Use fixed dimensions and proper flex
actionContainer: {
  flexDirection: 'row',
  justifyContent: 'space-evenly', // Not space-around
  paddingHorizontal: 20, // Add padding
},

actionButton: {
  width: 60, // Fixed size
  height: 60,
  // No transform that breaks layout
},
```

## 📱 **Expected Flow:**

### **Normal Accept Flow:**
1. `IncomingCallOverlayService.showIncomingCall()` called
2. Notification slides down with caller info
3. User taps Accept button
4. `handleAccept()` → `handleAcceptCall()` → `handleHideIncomingCall()`
5. Notification hides immediately
6. Navigate to CallScreen
7. WebRTC accepts call

### **Normal Reject Flow:**
1. User taps Reject button
2. `handleReject()` → `handleRejectCall()` → `handleHideIncomingCall()`
3. Notification hides immediately
4. WebRTC rejects call

### **Auto-Hide Flow:**
1. Caller ends call → WebRTC `onCallEnded` → `handleHideIncomingCall()`
2. 60s timeout → `setTimeout` → `handleHideIncomingCall()`
3. WebRTC error → `onError` → `handleHideIncomingCall()`

## 🎉 **Success Criteria:**

- ✅ **Stable UI**: No layout jumping or broken buttons
- ✅ **Working Actions**: Accept/Reject buttons work properly
- ✅ **Reliable Auto-Hide**: Notification never gets stuck
- ✅ **Proper Integration**: WebRTC and navigation work seamlessly
- ✅ **Good UX**: Smooth animations and immediate feedback

import {View, RefreshControl, SectionList, ScrollView} from 'react-native';
import HeaderLogo from '../../Screen/Layout/headers/HeaderLogo';
import CategoryGrid from '../category/CategoryGrid';
import {RootScreen} from '../../router/router';
import {useNavigation} from '@react-navigation/native';
import HotProductsRow from './HotProductsRow';
import HotProductsSection from './HotProductsSection';
import FreeShipProductSection from './section/FreeShipProductSection';
import ProductBestSeller from './productBestSeller';
import SuggestionProductSection from './section/SuggestionProductSection';
import BannerSection from './section/bannerSection';
import MuchSearchSearch from './section/MuchSearchSearch';
import {ColorThemes} from '../../assets/skin/colors';
import {useState} from 'react';

const ProductIndex = () => {
  const navigation = useNavigation<any>();
  const [refreshing, setRefreshing] = useState(false);
  const [isLoadmore, setIsLoadmore] = useState(false);
  const onSeeMore = (categoryId?: string) => {
    if (categoryId) {
      navigation.navigate(RootScreen.ProductListByCategory, {
        categoryId: categoryId,
      });
    } else {
      navigation.navigate(RootScreen.ProductListByCategory);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const sections = [
    {
      id: 'banner1',
      data: [
        <View key="banner1" style={{marginVertical: 12}}>
          <BannerSection />
        </View>,
      ],
    },
    {
      id: 'categories',
      data: [
        <CategoryGrid
          key="categories"
          numColumns={3}
          onCategoryPress={category => {
            onSeeMore(category.Id);
          }}
        />,
      ],
    },
    {
      id: 'hot_products',
      data: [
        <HotProductsSection
          key="hot_products"
          pageSize={10}
          onSeeAll={onSeeMore}
          onRefresh={refreshing}
        />,
      ],
    },
    {
      id: 'free_ship',
      data: [<FreeShipProductSection key="free_ship" onRefresh={refreshing} />],
    },
    {
      id: 'much_search',
      data: [
        <MuchSearchSearch
          key="much_search"
          onSeeMore={onSeeMore}
          onRefresh={refreshing}
        />,
      ],
    },
    {
      id: 'banner2',
      data: [
        <View key="banner2" style={{marginVertical: 12}}>
          <BannerSection />
        </View>,
      ],
    },
    {
      id: 'best_seller',
      data: [
        <ProductBestSeller
          key="best_seller"
          isSeeMore
          id="best-selling"
          onPressSeeMore={onSeeMore}
          onRefresh={refreshing}
        />,
      ],
    },
    {
      id: 'suggestions',
      data: [
        <SuggestionProductSection
          key="suggestions"
          onSeeAllPress={onSeeMore}
          onRefresh={refreshing}
          isLoadmore={isLoadmore}
        />,
      ],
    },
    {
      id: 'footer_space',
      data: [<View key="footer_space" style={{height: 220}} />],
    },
  ];

  const onLoadMore = () => {
    console.log('check-onLoadMore', isLoadmore);
    setIsLoadmore(true);
    setTimeout(() => {
      setIsLoadmore(false);
    }, 100);
  };

  return (
    <View
      style={{
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <HeaderLogo />

      <SectionList
        style={{height: '100%', width: '100%', paddingHorizontal: 16}}
        sections={sections}
        renderItem={({item}) => item}
        keyExtractor={(item, index) => item.key?.toString() ?? index.toString()}
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.1}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      />
    </View>
  );
};

export default ProductIndex;

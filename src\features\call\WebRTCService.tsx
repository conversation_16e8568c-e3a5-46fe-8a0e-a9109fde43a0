import { RTCPeerConnection, RTCSessionDescription, RTCIceCandidate, mediaDevices } from 'react-native-webrtc';
import SocketService from '../../modules/chat/services/SocketService';
import CallHistoryAPI from '../../modules/call/services/CallHistoryAPI';
import store from '../../redux/store/store';

export interface CallState {
  isInCall: boolean;
  isIncoming: boolean;
  isOutgoing: boolean;
  isConnected: boolean;
  targetUserId: string | null;
  targetUserName: string | null;
  callStartTime: Date | null;
}

export interface WebRTCCallbacks {
  onLocalStream?: (stream: any) => void;
  onRemoteStream?: (stream: any) => void;
  onCallReceived?: (data: { from: string; fromName?: string }) => void;
  onCallAccepted?: (data: { from: string }) => void;
  onCallRejected?: (data: { from: string }) => void;
  onCallEnded?: (data: { from: string }) => void;
  onError?: (error: any) => void;
  onCallStateChanged?: (state: CallState) => void;
}

class WebRTCService {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: any = null;
  private remoteStream: any = null;
  private isInitiator = false;
  private targetUserId: string | null = null;
  private targetUserName: string | null = null;
  private callTimeoutTimer: NodeJS.Timeout | null = null; // Timer cho timeout 60s
  private currentCallHistoryId: string | null = null; // ID của record CallHistory hiện tại
  private callAcceptedTime: Date | null = null; // Thời điểm cuộc gọi được chấp nhận
  private callState: CallState = {
    isInCall: false,
    isIncoming: false,
    isOutgoing: false,
    isConnected: false,
    targetUserId: null,
    targetUserName: null,
    callStartTime: null,
  };

  // ICE servers configuration
  private iceServers = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      // Thêm TURN servers nếu cần thiết cho production
    ]
  };

  // Callbacks
  private callbacks: WebRTCCallbacks = {};

  constructor() {
    // Không setup listeners ngay trong constructor
    // Sẽ setup khi có socket connection
  }

  // Thiết lập callbacks
  setCallbacks(callbacks: WebRTCCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // Cập nhật trạng thái cuộc gọi
  private updateCallState(updates: Partial<CallState>) {
    this.callState = { ...this.callState, ...updates };
    if (this.callbacks.onCallStateChanged) {
      this.callbacks.onCallStateChanged(this.callState);
    }
  }

  // Lấy trạng thái hiện tại
  getCallState(): CallState {
    return { ...this.callState };
  }

  // Thiết lập socket listeners - public method để có thể gọi từ bên ngoài
  setupSocketListeners() {
    const socket = SocketService.getSocket();
    if (!socket) {
      console.error('Socket not available for WebRTC');
      return;
    }

    // Nhận cuộc gọi đến
    socket.on('incoming-call', async (data: { from: string; socketId: string; fromName?: string }) => {
      console.log('Incoming call from:', data.from);
      this.targetUserId = data.from;
      this.isInitiator = false;
      this.updateCallState({
        isInCall: true,
        isIncoming: true,
        isOutgoing: false,
        targetUserId: data.from,
        targetUserName: data.fromName,
        callStartTime: new Date(),
      });

      if (this.callbacks.onCallReceived) {
        this.callbacks.onCallReceived({ from: data.from, fromName: data.fromName });
      }
    });

    // Cuộc gọi được chấp nhận
    socket.on('accept-call', async (data: { from: string, callHistoryId: string }) => {
      console.log('Call accepted by:', data.from);

      // Clear timeout khi cuộc gọi được chấp nhận
      this.clearCallTimeout();

      // Lưu thời điểm chấp nhận cuộc gọi
      this.callAcceptedTime = new Date();

      // Cập nhật CallHistory cho người gọi
      if (data.callHistoryId) {
        try {
          await CallHistoryAPI.updateCallHistory({
            Id: data.callHistoryId,
            IsAccept: true,
          });
          console.log('✅ Caller CallHistory updated: IsAccept = true');
        } catch (error) {
          console.error('❌ Failed to update caller CallHistory:', error);
        }
      }

      this.updateCallState({
        isConnected: true,
      });

      if (this.callbacks.onCallAccepted) {
        this.callbacks.onCallAccepted(data);
      }

      // Tạo offer nếu là người khởi tạo
      if (this.isInitiator && this.peerConnection) {
        try {
          const offer = await this.peerConnection.createOffer({});
          await this.peerConnection.setLocalDescription(offer);

          socket.emit('offer', {
            targetUserId: this.targetUserId,
            offer: offer
          });
        } catch (error) {
          console.error('Error creating offer:', error);
          this.handleError(error);
        }
      }
    });

    // Cuộc gọi bị từ chối
    socket.on('reject-call', (data: { from: string }) => {
      console.log('Call rejected by:', data.from);

      if (this.callbacks.onCallRejected) {
        this.callbacks.onCallRejected(data);
      }

      this.cleanup();
    });
    //on error
    socket.on('error', (error: any) => {
        debugger
      console.error('WebRTC Socket Error:', error);
      this.handleError(error);
    });

    // Nhận offer
    socket.on('offer', async (data: { offer: any; from: string }) => {
      console.log('Received offer from:', data.from);

      if (!this.peerConnection) {
        this.createPeerConnection();
      }

      try {
        await this.peerConnection!.setRemoteDescription(new RTCSessionDescription(data.offer));

        const answer = await this.peerConnection!.createAnswer();
        await this.peerConnection!.setLocalDescription(answer);

        socket.emit('answer', {
          targetUserId: data.from,
          answer: answer
        });
      } catch (error) {
        console.error('Error handling offer:', error);
        this.handleError(error);
      }
    });

    // Nhận answer
    socket.on('answer', async (data: { answer: any; from: string }) => {
      console.log('Received answer from:', data.from);

      try {
        await this.peerConnection!.setRemoteDescription(new RTCSessionDescription(data.answer));
      } catch (error) {
        console.error('Error handling answer:', error);
        this.handleError(error);
      }
    });

    // Nhận ICE candidate
    socket.on('candidate', async (candidate: any) => {
      console.log('Received ICE candidate');

      if (this.peerConnection) {
        try {
          await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
        } catch (error) {
          console.error('Error adding ICE candidate:', error);
        }
      }
    });

    // Cuộc gọi kết thúc
    socket.on('end-call', (data: { from: string }) => {
      console.log('Call ended by:', data.from);

      if (this.callbacks.onCallEnded) {
        this.callbacks.onCallEnded(data);
      }
      this.cleanup();
    });
  }

  // Tạo peer connection
  private createPeerConnection() {
    console.log('Creating peer connection with ICE servers:', this.iceServers);
    this.peerConnection = new RTCPeerConnection(this.iceServers);

    // Xử lý ICE candidates
    (this.peerConnection as any).onicecandidate = (event: any) => {
      if (event.candidate) {
        console.log('Sending ICE candidate');
        const socket = SocketService.getSocket();
        if (socket) {
          socket.emit('candidate', {
            candidate: event.candidate,
            targetUserId: this.targetUserId
          });
        }
      }
    };

    // Xử lý remote stream - sử dụng ontrack thay vì onaddstream
    (this.peerConnection as any).ontrack = (event: any) => {
      console.log('Received remote track');
      if (event.streams && event.streams[0]) {
        this.remoteStream = event.streams[0];
        if (this.callbacks.onRemoteStream) {
          this.callbacks.onRemoteStream(event.streams[0]);
        }
      }
    };

    // Xử lý connection state changes
    (this.peerConnection as any).onconnectionstatechange = () => {
      console.log('Connection state:', this.peerConnection?.connectionState);
      if (this.peerConnection?.connectionState === 'connected') {
        this.updateCallState({ isConnected: true });
      } else if (this.peerConnection?.connectionState === 'disconnected' ||
                 this.peerConnection?.connectionState === 'failed') {
        this.cleanup();
      }
    };
  }

  // Lấy local media stream (chỉ audio)
  private async getLocalStream(): Promise<any> {
    try {
      const constraints = {
        audio: true,
        video: false // Chỉ audio call
      };

      console.log('Requesting media permissions...');
      this.localStream = await mediaDevices.getUserMedia(constraints);
      console.log('Local stream obtained successfully');

      if (this.callbacks.onLocalStream) {
        this.callbacks.onLocalStream(this.localStream);
      }

      return this.localStream;
    } catch (error: any) {
      console.error('Error getting local stream:', error);
      // Thêm thông tin chi tiết về lỗi
      if (error?.name === 'NotAllowedError') {
        throw new Error('Microphone permission denied');
      } else if (error?.name === 'NotFoundError') {
        throw new Error('No microphone found');
      } else {
        throw new Error('Failed to access microphone: ' + (error?.message || 'Unknown error'));
      }
    }
  }

  // Bắt đầu cuộc gọi
  async startCall(targetUserId: string, targetUserName?: string): Promise<void> {
    try {
      debugger
      this.targetUserId = targetUserId;
      this.targetUserName = targetUserName || null;
      this.isInitiator = true;

      // Lấy thông tin user hiện tại
      const currentUser = store.getState().customer.data;
      if (!currentUser?.Id) {
        throw new Error('Current user not found');
      }

      // Tạo CallHistory record
      const callHistory = await CallHistoryAPI.createCallHistory({
        Name: this.targetUserName || 'Unknown',
        Receiver: targetUserId,
        CustomerId: currentUser.Id,
      });

      if (callHistory) {
        this.currentCallHistoryId = callHistory.Id;
        console.log('✅ CallHistory created:', callHistory.Id);
      } else {
        console.error('❌ Failed to create CallHistory');
      }

      this.updateCallState({
        isInCall: true,
        isOutgoing: true,
        isIncoming: false,
        targetUserId,
        targetUserName: this.targetUserName,
        callStartTime: new Date(),
      });

      // Tạo peer connection
      this.createPeerConnection();

      // Lấy local stream
      await this.getLocalStream();

      // Thêm local stream vào peer connection
      if (this.localStream && this.peerConnection) {
        console.log('Adding local stream tracks to peer connection');
        // Sử dụng addTrack thay vì addStream cho react-native-webrtc mới
        this.localStream.getTracks().forEach((track: any) => {
          console.log('Adding track:', track.kind);
          this.peerConnection!.addTrack(track, this.localStream);
        });
      }

      // Gửi call request
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('call-user', {
          targetUserId: targetUserId,
          fromName: this.targetUserName,
          callHistoryId: this.currentCallHistoryId,
        });
      }

      // Thiết lập timeout 60 giây
      this.startCallTimeout();

      console.log('Call initiated to:', targetUserId);
    } catch (error) {
      console.error('Error starting call:', error);
      this.handleError(error);
    }
  }

  // Chấp nhận cuộc gọi
  async acceptCall(): Promise<void> {
    try {
      // Clear timeout khi cuộc gọi được chấp nhận
      this.clearCallTimeout();

      // Lưu thời điểm chấp nhận cuộc gọi
      this.callAcceptedTime = new Date();

      // Cập nhật CallHistory nếu có
      if (this.currentCallHistoryId) {
        await CallHistoryAPI.updateCallHistory({
          Id: this.currentCallHistoryId,
          IsAccept: true,
        });
        console.log('✅ CallHistory updated: IsAccept = true');
      }

      // Tạo peer connection nếu chưa có
      if (!this.peerConnection) {
        this.createPeerConnection();
      }

      // Lấy local stream
      await this.getLocalStream();

      // Thêm local stream vào peer connection
      if (this.localStream && this.peerConnection) {
        // Sử dụng addTrack thay vì addStream cho react-native-webrtc mới
        this.localStream.getTracks().forEach((track: any) => {
          this.peerConnection!.addTrack(track, this.localStream);
        });
      }

      this.updateCallState({
        isIncoming: false,
        isConnected: true,
      });

      // Gửi accept call
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('accept-call', {
          from: this.targetUserId
        });
      }

      console.log('Call accepted');
    } catch (error) {
      console.error('Error accepting call:', error);
      this.handleError(error);
    }
  }

  // Từ chối cuộc gọi
  rejectCall(): void {
    const socket = SocketService.getSocket();
    if (socket) {
      socket.emit('reject-call', {
        from: this.targetUserId
      });
    }
    this.endCallWithReason('rejected');
  }

  // Kết thúc cuộc gọi
  endCall(): void {
    this.endCallWithReason('completed');
  }

  // Xử lý lỗi
  private handleError(error: any): void {
    console.error('WebRTC Error:', error);
    if (this.callbacks.onError) {
      this.callbacks.onError(error);
    }
    this.cleanup();
  }

  // Bắt đầu timeout timer cho cuộc gọi
  private startCallTimeout(): void {
    // Clear timeout cũ nếu có
    this.clearCallTimeout();

    // Thiết lập timeout 60 giây
    this.callTimeoutTimer = setTimeout(() => {
      console.log('⏰ Call timeout after 60 seconds');

      // Gọi callback timeout nếu có
      if (this.callbacks.onError) {
        this.callbacks.onError(new Error('Call timeout - No response after 60 seconds'));
      }

      // Kết thúc cuộc gọi
      this.endCallWithReason('timeout');
    }, 60000); // 60 giây

    console.log('⏰ Call timeout timer started (60s)');
  }

  // Xóa timeout timer
  private clearCallTimeout(): void {
    if (this.callTimeoutTimer) {
      clearTimeout(this.callTimeoutTimer);
      this.callTimeoutTimer = null;
      console.log('⏰ Call timeout timer cleared');
    }
  }

  // Kết thúc cuộc gọi với lý do cụ thể
  private async endCallWithReason(reason: 'timeout' | 'rejected' | 'completed' | 'cancelled'): Promise<void> {
    console.log('📞 Ending call with reason:', reason);

    // Clear timeout
    this.clearCallTimeout();

    // Cập nhật CallHistory với thời gian cuộc gọi
    if (this.currentCallHistoryId && this.callAcceptedTime) {
      try {
        const callDuration = Math.floor((new Date().getTime() - this.callAcceptedTime.getTime()) / 1000);
        await CallHistoryAPI.updateCallHistory({
          Id: this.currentCallHistoryId,
          Time: callDuration,
          EndReason: reason,
        });
        console.log(`✅ CallHistory updated: Time = ${callDuration}s, EndReason = ${reason}`);
      } catch (error) {
        console.error('❌ Failed to update CallHistory:', error);
      }
    } else if (this.currentCallHistoryId) {
      // Nếu cuộc gọi không được chấp nhận, chỉ cập nhật EndReason
      try {
        await CallHistoryAPI.updateCallHistory({
          Id: this.currentCallHistoryId,
          EndReason: reason,
        });
        console.log(`✅ CallHistory updated: EndReason = ${reason}`);
      } catch (error) {
        console.error('❌ Failed to update CallHistory:', error);
      }
    }

    // Gửi end call signal nếu cần
    if (reason !== 'timeout') {
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('end-call', {
          targetUserId: this.targetUserId
        });
      }
    }

    // Cleanup resources
    this.cleanup();
  }

  // Dọn dẹp resources
  private cleanup(): void {
    console.log('Cleaning up WebRTC resources');

    // Clear timeout timer
    this.clearCallTimeout();

    // Dừng local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach((track: any) => {
        track.stop();
      });
      this.localStream = null;
    }

    // Dừng remote stream
    if (this.remoteStream) {
      this.remoteStream.getTracks().forEach((track: any) => {
        track.stop();
      });
      this.remoteStream = null;
    }

    // Đóng peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    // Reset state
    this.isInitiator = false;
    this.targetUserId = null;
    this.targetUserName = null;
    this.currentCallHistoryId = null;
    this.callAcceptedTime = null;

    this.updateCallState({
      isInCall: false,
      isIncoming: false,
      isOutgoing: false,
      isConnected: false,
      targetUserId: null,
      targetUserName: null,
      callStartTime: null,
    });
  }

  // Kiểm tra xem có đang trong cuộc gọi không
  isInCall(): boolean {
    return this.callState.isInCall;
  }

  // Lấy thông tin người đang gọi
  getCurrentCallInfo(): { targetUserId: string | null; targetUserName: string | null } {
    return {
      targetUserId: this.targetUserId,
      targetUserName: this.targetUserName,
    };
  }
}

export default new WebRTCService();
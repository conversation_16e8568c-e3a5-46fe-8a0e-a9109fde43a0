import { RTCPeerConnection, RTCSessionDescription, RTCIceCandidate, mediaDevices } from 'react-native-webrtc';
import SocketService from '../../modules/chat/services/SocketService';

export interface CallState {
  isInCall: boolean;
  isIncoming: boolean;
  isOutgoing: boolean;
  isConnected: boolean;
  targetUserId: string | null;
  targetUserName: string | null;
  callStartTime: Date | null;
}

export interface WebRTCCallbacks {
  onLocalStream?: (stream: any) => void;
  onRemoteStream?: (stream: any) => void;
  onCallReceived?: (data: { from: string; fromName?: string }) => void;
  onCallAccepted?: (data: { from: string }) => void;
  onCallRejected?: (data: { from: string }) => void;
  onCallEnded?: (data: { from: string }) => void;
  onError?: (error: any) => void;
  onCallStateChanged?: (state: CallState) => void;
}

class WebRTCService {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: any = null;
  private remoteStream: any = null;
  private isInitiator = false;
  private targetUserId: string | null = null;
  private targetUserName: string | null = null;
  private callState: CallState = {
    isInCall: false,
    isIncoming: false,
    isOutgoing: false,
    isConnected: false,
    targetUserId: null,
    targetUserName: null,
    callStartTime: null,
  };

  // ICE servers configuration
  private iceServers = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      // Thêm TURN servers nếu cần thiết cho production
    ]
  };

  // Callbacks
  private callbacks: WebRTCCallbacks = {};

  constructor() {
    // Không setup listeners ngay trong constructor
    // Sẽ setup khi có socket connection
  }

  // Thiết lập callbacks
  setCallbacks(callbacks: WebRTCCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // Cập nhật trạng thái cuộc gọi
  private updateCallState(updates: Partial<CallState>) {
    this.callState = { ...this.callState, ...updates };
    if (this.callbacks.onCallStateChanged) {
      this.callbacks.onCallStateChanged(this.callState);
    }
  }

  // Lấy trạng thái hiện tại
  getCallState(): CallState {
    return { ...this.callState };
  }

  // Thiết lập socket listeners - public method để có thể gọi từ bên ngoài
  setupSocketListeners() {
    const socket = SocketService.getSocket();
    if (!socket) {
      console.error('Socket not available for WebRTC');
      return;
    }

    // Nhận cuộc gọi đến
    socket.on('incoming-call', (data: { from: string; socketId: string }) => {
      console.log('Incoming call from:', data.from);
      this.targetUserId = data.from;
      this.isInitiator = false;

      this.updateCallState({
        isInCall: true,
        isIncoming: true,
        isOutgoing: false,
        targetUserId: data.from,
        callStartTime: new Date(),
      });

      if (this.callbacks.onCallReceived) {
        this.callbacks.onCallReceived({ from: data.from });
      }
    });

    // Cuộc gọi được chấp nhận
    socket.on('accept-call', async (data: { from: string }) => {
      console.log('Call accepted by:', data.from);

      this.updateCallState({
        isConnected: true,
      });

      if (this.callbacks.onCallAccepted) {
        this.callbacks.onCallAccepted(data);
      }

      // Tạo offer nếu là người khởi tạo
      if (this.isInitiator && this.peerConnection) {
        try {
          const offer = await this.peerConnection.createOffer({});
          await this.peerConnection.setLocalDescription(offer);

          socket.emit('offer', {
            targetUserId: this.targetUserId,
            offer: offer
          });
        } catch (error) {
          console.error('Error creating offer:', error);
          this.handleError(error);
        }
      }
    });

    // Cuộc gọi bị từ chối
    socket.on('reject-call', (data: { from: string }) => {
      console.log('Call rejected by:', data.from);

      if (this.callbacks.onCallRejected) {
        this.callbacks.onCallRejected(data);
      }

      this.cleanup();
    });

    // Nhận offer
    socket.on('offer', async (data: { offer: any; from: string }) => {
      console.log('Received offer from:', data.from);

      if (!this.peerConnection) {
        this.createPeerConnection();
      }

      try {
        await this.peerConnection!.setRemoteDescription(new RTCSessionDescription(data.offer));

        const answer = await this.peerConnection!.createAnswer();
        await this.peerConnection!.setLocalDescription(answer);

        socket.emit('answer', {
          targetUserId: data.from,
          answer: answer
        });
      } catch (error) {
        console.error('Error handling offer:', error);
        this.handleError(error);
      }
    });

    // Nhận answer
    socket.on('answer', async (data: { answer: any; from: string }) => {
      console.log('Received answer from:', data.from);

      try {
        await this.peerConnection!.setRemoteDescription(new RTCSessionDescription(data.answer));
      } catch (error) {
        console.error('Error handling answer:', error);
        this.handleError(error);
      }
    });

    // Nhận ICE candidate
    socket.on('candidate', async (candidate: any) => {
      console.log('Received ICE candidate');

      if (this.peerConnection) {
        try {
          await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
        } catch (error) {
          console.error('Error adding ICE candidate:', error);
        }
      }
    });

    // Cuộc gọi kết thúc
    socket.on('end-call', (data: { from: string }) => {
      console.log('Call ended by:', data.from);

      if (this.callbacks.onCallEnded) {
        this.callbacks.onCallEnded(data);
      }

      this.cleanup();
    });
  }

  // Tạo peer connection
  private createPeerConnection() {
    this.peerConnection = new RTCPeerConnection(this.iceServers);

    // Xử lý ICE candidates
    (this.peerConnection as any).onicecandidate = (event: any) => {
      if (event.candidate) {
        console.log('Sending ICE candidate');
        const socket = SocketService.getSocket();
        if (socket) {
          socket.emit('candidate', {
            candidate: event.candidate,
            targetUserId: this.targetUserId
          });
        }
      }
    };

    // Xử lý remote stream
    (this.peerConnection as any).onaddstream = (event: any) => {
      console.log('Received remote stream');
      this.remoteStream = event.stream;
      if (this.callbacks.onRemoteStream) {
        this.callbacks.onRemoteStream(event.stream);
      }
    };

    // Xử lý connection state changes
    (this.peerConnection as any).onconnectionstatechange = () => {
      console.log('Connection state:', this.peerConnection?.connectionState);
      if (this.peerConnection?.connectionState === 'connected') {
        this.updateCallState({ isConnected: true });
      } else if (this.peerConnection?.connectionState === 'disconnected' ||
                 this.peerConnection?.connectionState === 'failed') {
        this.cleanup();
      }
    };
  }

  // Lấy local media stream (chỉ audio)
  private async getLocalStream(): Promise<any> {
    try {
      const constraints = {
        audio: true,
        video: false // Chỉ audio call
      };

      this.localStream = await mediaDevices.getUserMedia(constraints);

      if (this.callbacks.onLocalStream) {
        this.callbacks.onLocalStream(this.localStream);
      }

      return this.localStream;
    } catch (error) {
      console.error('Error getting local stream:', error);
      throw error;
    }
  }

  // Bắt đầu cuộc gọi
  async startCall(targetUserId: string, targetUserName?: string): Promise<void> {
    try {
      this.targetUserId = targetUserId;
      this.targetUserName = targetUserName || null;
      this.isInitiator = true;

      this.updateCallState({
        isInCall: true,
        isOutgoing: true,
        isIncoming: false,
        targetUserId,
        targetUserName: this.targetUserName,
        callStartTime: new Date(),
      });

      // Tạo peer connection
      this.createPeerConnection();

      // Lấy local stream
      await this.getLocalStream();

      // Thêm local stream vào peer connection
      if (this.localStream && this.peerConnection) {
        (this.peerConnection as any).addStream(this.localStream);
      }

      // Gửi call request
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('call-user', {
          targetUserId: targetUserId
        });
      }

      console.log('Call initiated to:', targetUserId);
    } catch (error) {
      console.error('Error starting call:', error);
      this.handleError(error);
    }
  }

  // Chấp nhận cuộc gọi
  async acceptCall(): Promise<void> {
    try {
      // Tạo peer connection nếu chưa có
      if (!this.peerConnection) {
        this.createPeerConnection();
      }

      // Lấy local stream
      await this.getLocalStream();

      // Thêm local stream vào peer connection
      if (this.localStream && this.peerConnection) {
        (this.peerConnection as any).addStream(this.localStream);
      }

      this.updateCallState({
        isIncoming: false,
        isConnected: true,
      });

      // Gửi accept call
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('accept-call', {
          from: this.targetUserId
        });
      }

      console.log('Call accepted');
    } catch (error) {
      console.error('Error accepting call:', error);
      this.handleError(error);
    }
  }

  // Từ chối cuộc gọi
  rejectCall(): void {
    const socket = SocketService.getSocket();
    if (socket) {
      socket.emit('reject-call', {
        from: this.targetUserId
      });
    }
    this.cleanup();
  }

  // Kết thúc cuộc gọi
  endCall(): void {
    const socket = SocketService.getSocket();
    if (socket) {
      socket.emit('end-call', {
        targetUserId: this.targetUserId
      });
    }
    this.cleanup();
  }

  // Xử lý lỗi
  private handleError(error: any): void {
    console.error('WebRTC Error:', error);
    if (this.callbacks.onError) {
      this.callbacks.onError(error);
    }
    this.cleanup();
  }

  // Dọn dẹp resources
  private cleanup(): void {
    console.log('Cleaning up WebRTC resources');

    // Dừng local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach((track: any) => {
        track.stop();
      });
      this.localStream = null;
    }

    // Dừng remote stream
    if (this.remoteStream) {
      this.remoteStream.getTracks().forEach((track: any) => {
        track.stop();
      });
      this.remoteStream = null;
    }

    // Đóng peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    // Reset state
    this.isInitiator = false;
    this.targetUserId = null;
    this.targetUserName = null;

    this.updateCallState({
      isInCall: false,
      isIncoming: false,
      isOutgoing: false,
      isConnected: false,
      targetUserId: null,
      targetUserName: null,
      callStartTime: null,
    });
  }

  // Kiểm tra xem có đang trong cuộc gọi không
  isInCall(): boolean {
    return this.callState.isInCall;
  }

  // Lấy thông tin người đang gọi
  getCurrentCallInfo(): { targetUserId: string | null; targetUserName: string | null } {
    return {
      targetUserId: this.targetUserId,
      targetUserName: this.targetUserName,
    };
  }
}

export default new WebRTCService();
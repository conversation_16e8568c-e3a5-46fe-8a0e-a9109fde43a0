// WebRTCService.js
import { RTCPeerConnection, RTCSessionDescription, RTCIceCandidate, mediaDevices } from 'react-native-webrtc';
import { io } from 'socket.io-client';


class WebRTCService {
  constructor() {
    this.socket = null;
    this.peerConnection = null;
    this.localStream = null;
    this.remoteStream = null;
    this.isInitiator = false;
    this.targetUserId = null;
    
    // ICE servers configuration
    this.iceServers = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        // Thêm TURN servers nếu cần thiết cho production
      ]
    };

    // Callbacks
    this.onLocalStream = null;
    this.onRemoteStream = null;
    this.onCallReceived = null;
    this.onCallAccepted = null;
    this.onCallRejected = null;
    this.onCallEnded = null;
    this.onError = null;
  }

  // Khởi tạo socket connection
  initialize(serverUrl, customerId) {
    this.customerId = customerId;
    this.socket = io(serverUrl, {
      transports: ['websocket'],
      query: {
        CustomerId: customerId
      }
    });

    this.setupSocketListeners();
  }

  // Thiết lập các socket listeners
  setupSocketListeners() {
    this.socket.on('connect', () => {
      console.log('Connected to signaling server');
    });

    this.socket.on('incoming-call', (data) => {
      console.log('Incoming call from:', data.from);
      this.targetUserId = data.from;
      this.isInitiator = false;
      if (this.onCallReceived) {
        this.onCallReceived(data);
      }
    });

    this.socket.on('accept-call', async (data) => {
      console.log('Call accepted by:', data.from);
      if (this.onCallAccepted) {
        this.onCallAccepted(data);
      }
      // Bắt đầu quá trình tạo offer
      await this.createOffer();
    });

    this.socket.on('reject-call', (data) => {
      console.log('Call rejected by:', data.from);
      if (this.onCallRejected) {
        this.onCallRejected(data);
      }
      this.cleanup();
    });

    this.socket.on('offer', async (data) => {
      console.log('Received offer from:', data.from);
      await this.handleOffer(data.offer);
    });

    this.socket.on('answer', async (data) => {
      console.log('Received answer from:', data.from);
      await this.handleAnswer(data.answer);
    });

    this.socket.on('candidate', async (data) => {
      console.log('Received ICE candidate');
      await this.handleCandidate(data);
    });

    this.socket.on('end-call', () => {
      console.log('Call ended');
      if (this.onCallEnded) {
        this.onCallEnded();
      }
      this.cleanup();
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
      if (this.onError) {
        this.onError(error);
      }
    });
  }

  // Tạo peer connection
  createPeerConnection() {
    this.peerConnection = new RTCPeerConnection(this.iceServers);

    // Xử lý ICE candidates
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        console.log('Sending ICE candidate');
        this.socket.emit('candidate', {
          candidate: event.candidate,
          targetUserId: this.targetUserId
        });
      }
    };

    // Xử lý remote stream
    this.peerConnection.onaddstream = (event) => {
      console.log('Received remote stream');
      this.remoteStream = event.stream;
      if (this.onRemoteStream) {
        this.onRemoteStream(event.stream);
      }
    };

    // Xử lý connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      console.log('Connection state:', this.peerConnection.connectionState);
      if (this.peerConnection.connectionState === 'failed') {
        console.error('Connection failed');
        if (this.onError) {
          this.onError('Connection failed');
        }
      }
    };

    // Xử lý ICE connection state
    this.peerConnection.oniceconnectionstatechange = () => {
      console.log('ICE connection state:', this.peerConnection.iceConnectionState);
    };
  }

  // Lấy local media stream
  async getLocalStream(isVideoCall = true) {
    try {
      const constraints = {
        audio: true,
        video: isVideoCall ? {
          width: { min: 640, ideal: 1280, max: 1920 },
          height: { min: 480, ideal: 720, max: 1080 },
          frameRate: { min: 16, ideal: 30, max: 30 }
        } : false
      };

      this.localStream = await mediaDevices.getUserMedia(constraints);
      
      if (this.onLocalStream) {
        this.onLocalStream(this.localStream);
      }

      return this.localStream;
    } catch (error) {
      console.error('Error getting local stream:', error);
      throw error;
    }
  }

  // Bắt đầu cuộc gọi
  async startCall(targetUserId, isVideoCall = true) {
    try {
      this.targetUserId = targetUserId;
      this.isInitiator = true;

      // Tạo peer connection
      this.createPeerConnection();

      // Lấy local stream
      await this.getLocalStream(isVideoCall);

      // Thêm local stream vào peer connection
      this.peerConnection.addStream(this.localStream);

      // Gửi call request
      this.socket.emit('call-user', {
        targetUserId: targetUserId
      });

      console.log('Call initiated to:', targetUserId);
    } catch (error) {
      console.error('Error starting call:', error);
      if (this.onError) {
        this.onError(error);
      }
    }
  }

  // Chấp nhận cuộc gọi
  async acceptCall(isVideoCall = true) {
    try {
      // Tạo peer connection
      this.createPeerConnection();

      // Lấy local stream
      await this.getLocalStream(isVideoCall);

      // Thêm local stream vào peer connection
      this.peerConnection.addStream(this.localStream);

      // Gửi accept call
      this.socket.emit('accept-call', {
        from: this.targetUserId
      });

      console.log('Call accepted');
    } catch (error) {
      console.error('Error accepting call:', error);
      if (this.onError) {
        this.onError(error);
      }
    }
  }

  // Từ chối cuộc gọi
  rejectCall() {
    this.socket.emit('reject-call', {
      from: this.targetUserId
    });
    this.cleanup();
  }

  // Tạo offer
  async createOffer() {
    try {
      const offer = await this.peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true
      });

      await this.peerConnection.setLocalDescription(offer);

      this.socket.emit('offer', {
        offer: offer,
        targetUserId: this.targetUserId
      });

      console.log('Offer created and sent');
    } catch (error) {
      console.error('Error creating offer:', error);
      if (this.onError) {
        this.onError(error);
      }
    }
  }

  // Xử lý offer
  async handleOffer(offer) {
    try {
      await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));

      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);

      this.socket.emit('answer', {
        answer: answer,
        targetUserId: this.targetUserId
      });

      console.log('Answer created and sent');
    } catch (error) {
      console.error('Error handling offer:', error);
      if (this.onError) {
        this.onError(error);
      }
    }
  }

  // Xử lý answer
  async handleAnswer(answer) {
    try {
      await this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
      console.log('Answer processed');
    } catch (error) {
      console.error('Error handling answer:', error);
      if (this.onError) {
        this.onError(error);
      }
    }
  }

  // Xử lý ICE candidate
  async handleCandidate(candidate) {
    try {
      await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
      console.log('ICE candidate added');
    } catch (error) {
      console.error('Error handling candidate:', error);
    }
  }

  // Kết thúc cuộc gọi
  endCall() {
    this.socket.emit('end-call', {
      targetUserId: this.targetUserId
    });
    this.cleanup();
  }

  // Tắt/bật microphone
  toggleMicrophone() {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        return audioTrack.enabled;
      }
    }
    return false;
  }

  // Tắt/bật camera
  toggleCamera() {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        return videoTrack.enabled;
      }
    }
    return false;
  }

  // Chuyển đổi camera (front/back)
  async switchCamera() {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        await videoTrack._switchCamera();
      }
    }
  }

  // Dọn dẹp resources
  cleanup() {
    console.log('Cleaning up WebRTC resources');

    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.remoteStream) {
      this.remoteStream.getTracks().forEach(track => track.stop());
      this.remoteStream = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.targetUserId = null;
    this.isInitiator = false;
  }

  // Ngắt kết nối
  disconnect() {
    this.cleanup();
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
}

export default new WebRTCService();
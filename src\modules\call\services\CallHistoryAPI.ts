import { DataController } from '../../../base/baseController';
import { randomGID } from '../../../utils/Utils';
import { 
  CallHistory, 
  CreateCallHistoryRequest, 
  UpdateCallHistoryRequest, 
  CallHistoryResponse,
  CallStatistics 
} from '../types/CallTypes';

class CallHistoryAPI {
  private callHistoryController: DataController;

  constructor() {
    this.callHistoryController = new DataController('CallHistory');
  }

  // Tạo record CallHistory mới khi bắt đầu cuộc gọi
  async createCallHistory(request: CreateCallHistoryRequest): Promise<CallHistory | null> {
    try {
      const callHistoryData: CallHistory = {
        Id: randomGID(),
        DateCreated: new Date().getTime(),
        Name: request.Name,
        Receiver: request.Receiver,
        IsAccept: false, // Mặc định là false khi mới tạo
        Time: 0, // Mặc định là 0 khi mới tạo
        CustomerId: request.CustomerId,
      };
      const response = await this.callHistoryController.add([callHistoryData]);
      
      if (response.code === 200 && response.data.length > 0) {
        return response.data[0];
      }      
      return null;
    } catch (error) {
      throw error;
    }
  }

  // Cập nhật CallHistory (khi accept call, end call, etc.)
  async updateCallHistory(request: UpdateCallHistoryRequest): Promise<boolean> {
    try {
      const updateData: Partial<CallHistory> = {
        Id: request.Id,
      };

      if (request.IsAccept !== undefined) {
        updateData.IsAccept = request.IsAccept;
      }

      if (request.Time !== undefined) {
        updateData.Time = request.Time;
      }

      if (request.EndReason !== undefined) {
        updateData.EndReason = request.EndReason;
      }

      const response = await this.callHistoryController.edit([updateData]);
      
      if (response.code === 200) {
        console.log('✅ CallHistory updated:', updateData);
        return true;
      }
      
      console.error('❌ Failed to update CallHistory:', response);
      return false;
    } catch (error) {
      console.error('Error updating call history:', error);
      throw error;
    }
  }

  // Lấy lịch sử cuộc gọi của user
  async getCallHistory(
    userId: string, 
    page: number = 1, 
    size: number = 50
  ): Promise<CallHistoryResponse> {
    try {
      // Tìm các cuộc gọi mà user là người gọi hoặc người nhận
      const response = await this.callHistoryController.getPatternList({
        page,
        size,
        query: `@CallerId:{${userId}} | @Receiver:{${userId}}`,
        returns: ['Id', 'DateCreated', 'Name', 'Receiver', 'IsAccept', 'Time', 'CallerId', 'CallerName', 'CallType', 'EndReason'],
        pattern: {
          CallerId: ['Id', 'Name', 'AvatarUrl'],
          Receiver: ['Id', 'Name', 'AvatarUrl'],
        },
      });

      if (response.code === 200) {
        // Xử lý data để xác định call type cho từng user
        const processedData = response.data.map((item: any) => {
          const isOutgoing = item.CallerId === userId;
          const isAccepted = item.IsAccept;
          
          let callType: 'incoming' | 'outgoing' | 'missed';
          if (isOutgoing) {
            callType = isAccepted ? 'outgoing' : 'missed';
          } else {
            callType = isAccepted ? 'incoming' : 'missed';
          }

          return {
            ...item,
            CallType: callType,
          };
        });

        return {
          data: processedData || [],
          total: response.total || 0,
        };
      }

      return {
        data: [],
        total: 0,
      };
    } catch (error) {
      console.error('Error fetching call history:', error);
      throw error;
    }
  }

  // Lấy thống kê cuộc gọi
  async getCallStatistics(userId: string): Promise<CallStatistics> {
    try {
      const response = await this.getCallHistory(userId, 1, 1000); // Lấy tất cả để tính toán
      const callHistory = response.data;

      const totalCalls = callHistory.length;
      const acceptedCalls = callHistory.filter(call => call.IsAccept).length;
      const missedCalls = totalCalls - acceptedCalls;
      const totalDuration = callHistory.reduce((sum, call) => sum + (call.Time || 0), 0);
      const averageDuration = acceptedCalls > 0 ? totalDuration / acceptedCalls : 0;

      return {
        totalCalls,
        acceptedCalls,
        missedCalls,
        totalDuration,
        averageDuration,
      };
    } catch (error) {
      console.error('Error getting call statistics:', error);
      throw error;
    }
  }

  // Xóa lịch sử cuộc gọi (nếu cần)
  async deleteCallHistory(callId: string): Promise<boolean> {
    try {
      const response = await this.callHistoryController.delete([callId]);
      
      if (response.code === 200) {
        console.log('✅ CallHistory deleted:', callId);
        return true;
      }
      
      console.error('❌ Failed to delete CallHistory:', response);
      return false;
    } catch (error) {
      console.error('Error deleting call history:', error);
      throw error;
    }
  }

  // Lấy một record CallHistory theo ID
  async getCallHistoryById(callId: string): Promise<CallHistory | null> {
    try {
      const response = await this.callHistoryController.getPatternList({
        page: 1,
        size: 1,
        query: `@Id:{${callId}}`,
        returns: ['Id', 'DateCreated', 'Name', 'Receiver', 'IsAccept', 'Time', 'CallerId', 'CallerName', 'CallType', 'EndReason'],
      });

      if (response.code === 200 && response.data.length > 0) {
        return response.data[0];
      }
      
      return null;
    } catch (error) {
      console.error('Error getting call history by ID:', error);
      throw error;
    }
  }
}

export default new CallHistoryAPI();

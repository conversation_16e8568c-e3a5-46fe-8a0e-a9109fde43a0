import {
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {navigateBack} from '../../../router/router';
import {
  ComponentStatus,
  FDialog,
  ListTile,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import React, {useState, useEffect, useRef} from 'react';
import ReactNativeBiometrics from 'react-native-biometrics';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {FaceID, TouchID} from '../../../features/local-authen/local-authen';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {SafeAreaView} from 'react-native-safe-area-context';
import ScreenHeader from '../../../Screen/Layout/header';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import IOSSwitch from '../../../components/IOSSwitch';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';

export default function BiometricSetting() {
  const customer = useSelectorCustomerState().data;
  const [biometric, setBiometric] = useState(false);
  const [biometricType, setBiometricType] = useState<any>();
  const [avaiBiometric, setAvaiBiometric] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [password, setPassword] = useState('');
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [pendingValue, setPendingValue] = useState(false);
  const rnBiometrics = new ReactNativeBiometrics({
    allowDeviceCredentials: true,
  });
  const dialogRef = useRef<any>(null);
  useEffect(() => {
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        setAvaiBiometric(true);
        getDataToAsyncStorage('biometryType').then(result => {
          setBiometricType(result);
        });
        getDataToAsyncStorage('Biometrics').then(result => {
          setBiometric(result == 'true' ? true : false);
        });
      } else {
        setAvaiBiometric(false);
        setBiometric(false);
      }
    });
  }, []);

  const checkPassword = (vl: boolean) => {
    setPendingValue(vl);
    setShowPasswordModal(true);
  };

  const handlePasswordConfirm = async () => {
    if (!password.trim()) {
      showSnackbar({
        message: 'Vui lòng nhập mật khẩu',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    try {
      const res = await CustomerActions.checkPassword(
        customer?.Mobile || '',
        password,
      );

      if (res.code === 200) {
        // Mật khẩu đúng, đóng modal và gọi biometric prompt
        setShowPasswordModal(false);
        saveDataToAsyncStorage('Password', password);
        handleBiometricPrompt(pendingValue);
        setPassword('');
      } else {
        // Mật khẩu sai
        showSnackbar({
          message: 'Mật khẩu không đúng',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      showSnackbar({
        message: 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const handleBiometricPrompt = (vl: boolean) => {
    rnBiometrics
      .simplePrompt({promptMessage: 'Xác nhận sinh trắc học'})
      .then(resultObject => {
        const {success} = resultObject;
        if (success) {
          if (vl) {
            setTimeout(() => {
              saveDataToAsyncStorage('Biometrics', 'true');
              setBiometric(true);
            }, 100);
            showSnackbar({
              message: 'Bật sinh trắc học thành công',
              status: ComponentStatus.SUCCSESS,
            });
            return;
          } else {
            setTimeout(() => {
              saveDataToAsyncStorage('Biometrics', 'false');
              setBiometric(false);
            }, 100);
          }
        } else {
          setTimeout(() => {
            saveDataToAsyncStorage('Biometrics', biometric ? 'true' : 'false');
            setBiometric(biometric);
          }, 100);
          console.log('user cancelled biometric prompt', biometric);
        }
      })
      .catch(() => {
        setTimeout(() => {
          saveDataToAsyncStorage('Biometrics', 'false');
          setBiometric(false);
        }, 100);
        console.log('biometrics failed', biometric);
      });
  };

  const handleCloseModal = () => {
    setShowPasswordModal(false);
    setPassword('');
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogRef} />
      <InforHeader
        title="Cài đặt sinh trắc học"
        onBack={() => navigateBack()}
      />
      <View style={{paddingHorizontal: 16, paddingTop: 16}}>
        <Text
          style={{
            ...TypoSkin.body3,
            color: ColorThemes.light.neutral_text_subtitle_color,
          }}>
          Sinh trắc học là một tính năng bảo mật cho phép bạn đăng nhập bằng vân
          tay hoặc khuôn mặt.
        </Text>
      </View>
      <ListTile
        leading={
          <View>
            {(() => {
              if (biometricType == 'TouchID') {
                return (
                  <TouchID
                    size={20}
                    color={
                      avaiBiometric
                        ? ColorThemes.light.neutral_text_subtitle_color
                        : ColorThemes.light.neutral_text_disabled_color
                    }
                  />
                );
              } else {
                return (
                  <FaceID
                    size={20}
                    color={
                      avaiBiometric
                        ? ColorThemes.light.neutral_text_subtitle_color
                        : ColorThemes.light.neutral_text_disabled_color
                    }
                  />
                );
              }
            })()}
          </View>
        }
        title="Sử dụng sinh trắc học"
        titleStyle={[
          TypoSkin.heading8,
          {
            color: avaiBiometric
              ? ColorThemes.light.Neutral_Text_Color_Title
              : ColorThemes.light.neutral_text_disabled_color,
          },
        ]}
        trailing={
          <IOSSwitch
            value={biometric}
            disabled={!avaiBiometric}
            onColor={ColorThemes.light.primary_main_color}
            onValueChange={checkPassword}
          />
        }
      />

      {/* Password Confirmation Modal */}
      <Modal
        visible={showPasswordModal}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCloseModal}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View
            style={{
              flex: 1,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              justifyContent: 'center',
              alignItems: 'center',
              paddingHorizontal: 20,
            }}>
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
              <KeyboardAvoidingView
                style={{
                  backgroundColor:
                    ColorThemes.light.neutral_absolute_background_color,
                  borderRadius: 12,
                  padding: 24,
                  width: '100%',
                  maxWidth: 400,
                }}>
                <Text
                  style={{
                    ...TypoSkin.heading6,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'center',
                    marginBottom: 16,
                  }}>
                  Xác nhận mật khẩu
                </Text>

                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                    textAlign: 'center',
                    marginBottom: 24,
                  }}>
                  Vui lòng nhập mật khẩu để xác nhận thay đổi cài đặt sinh trắc
                  học
                </Text>

                <View
                  style={{
                    borderWidth: 1,
                    borderColor: ColorThemes.light.neutral_text_subtitle_color,
                    borderRadius: 8,
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 24,
                  }}>
                  <TextInput
                    style={{
                      flex: 1,
                      height: 48,
                      paddingHorizontal: 16,
                      ...TypoSkin.body2,
                      color: ColorThemes.light.neutral_text_body_color,
                    }}
                    placeholder="Nhập mật khẩu của bạn"
                    placeholderTextColor={
                      ColorThemes.light.neutral_text_disabled_color
                    }
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={isVisiblePass}
                    autoFocus={true}
                  />
                  <TouchableOpacity
                    style={{padding: 12}}
                    onPress={() => setVisiblePass(!isVisiblePass)}>
                    <Winicon
                      src={
                        isVisiblePass
                          ? `outline/user interface/view`
                          : `outline/user interface/hide`
                      }
                      size={14}
                      color={ColorThemes.light.neutral_text_subtitle_color}
                    />
                  </TouchableOpacity>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    gap: 12,
                  }}>
                  <TouchableOpacity
                    style={{
                      flex: 1,
                      height: 48,
                      backgroundColor:
                        ColorThemes.light.neutral_text_subtitle_color,
                      borderRadius: 8,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    onPress={handleCloseModal}>
                    <Text
                      style={{
                        ...TypoSkin.buttonText3,
                        color: ColorThemes.light.neutral_text_body_color,
                      }}>
                      Hủy
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={{
                      flex: 1,
                      height: 48,
                      backgroundColor: ColorThemes.light.primary_main_color,
                      borderRadius: 8,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    onPress={handlePasswordConfirm}>
                    <Text
                      style={{
                        ...TypoSkin.buttonText3,
                        color:
                          ColorThemes.light.neutral_absolute_background_color,
                      }}>
                      Xác nhận
                    </Text>
                  </TouchableOpacity>
                </View>
              </KeyboardAvoidingView>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
}

import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  StatusBar,
  Platform,
} from 'react-native';
import { ColorThemes } from '../../assets/skin/colors';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../Config/ConfigAPI';
import { Winicon } from 'wini-mobile-components';



interface IncomingCallNotificationProps {
  visible: boolean;
  callerName: string;
  callerAvatar?: string;
  onAccept: () => void;
  onReject: () => void;
}

const IncomingCallNotification: React.FC<IncomingCallNotificationProps> = ({
  visible,
  callerName,
  callerAvatar,
  onAccept,
  onReject,
}) => {
  const slideAnim = useRef(new Animated.Value(-200)).current; // Start from top
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (visible) {
      // Slide down animation from top
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      // Pulse animation for avatar
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => {
        pulseAnimation.stop();
      };
    } else {
      // Slide up animation to hide
      Animated.timing(slideAnim, {
        toValue: -200,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <StatusBar barStyle="light-content" backgroundColor="rgba(0,0,0,0.5)" />

      {/* Background overlay */}
      <View style={styles.overlay} />

      {/* Notification content - giống design notification */}
      <View style={styles.notificationCard}>
        {/* Caller info */}
        <View style={styles.callerSection}>
          <Animated.View
            style={[
              styles.avatarContainer,
              {
                transform: [{ scale: pulseAnim }],
              },
            ]}
          >
            {callerAvatar ? (
              <FastImage
                source={{ uri: ConfigAPI.urlImg + callerAvatar }}
                style={styles.avatar}
              />
            ) : (
              <View style={[styles.avatar, styles.defaultAvatar]}>
                <Text style={styles.avatarText}>
                  {callerName?.charAt(0).toUpperCase() || '?'}
                </Text>
              </View>
            )}
          </Animated.View>

          <View style={styles.callerDetails}>
            <Text style={styles.callerName}>{callerName || 'Người dùng'}</Text>
            <Text style={styles.callerSubtext}>Cuộc gọi đến</Text>
          </View>

          <View style={styles.callIcon}>
            <Winicon src="fill/user interface/phone-call" size={20} color={ColorThemes.light.primary_color} />
          </View>
        </View>

        {/* Action buttons */}
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={onReject}
            activeOpacity={0.8}
          >
            <Winicon src="fill/user interface/phone-call" size={24} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.acceptButton]}
            onPress={onAccept}
            activeOpacity={0.8}
          >
            <Winicon src="fill/user interface/phone-call" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 200, // Chiều cao cố định như notification
    zIndex: 9999,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Overlay nhẹ hơn
  },
  notificationCard: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginTop: Platform.OS === 'ios' ? 50 : (StatusBar.currentHeight || 0) + 10,
    borderRadius: 12,
    padding: 16,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  callerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  callerDetails: {
    flex: 1,
  },
  callerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  callerSubtext: {
    fontSize: 14,
    color: '#666',
  },
  callIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: 8,
  },
  actionButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  rejectButton: {
    backgroundColor: '#F44336',
    transform: [{ rotate: '135deg' }],
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
});

export default IncomingCallNotification;

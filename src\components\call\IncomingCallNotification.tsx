import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
  Platform,
} from 'react-native';
import { ColorThemes } from '../../assets/skin/colors';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../Config/ConfigAPI';
import { Winicon } from 'wini-mobile-components';

const { width, height } = Dimensions.get('window');

interface IncomingCallNotificationProps {
  visible: boolean;
  callerName: string;
  callerAvatar?: string;
  onAccept: () => void;
  onReject: () => void;
}

const IncomingCallNotification: React.FC<IncomingCallNotificationProps> = ({
  visible,
  callerName,
  callerAvatar,
  onAccept,
  onReject,
}) => {
  const slideAnim = useRef(new Animated.Value(-height)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (visible) {
      // Slide down animation
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      // Pulse animation for avatar
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => {
        pulseAnimation.stop();
      };
    } else {
      // Slide up animation
      Animated.timing(slideAnim, {
        toValue: -height,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <StatusBar barStyle="light-content" backgroundColor="rgba(0,0,0,0.8)" />
      
      {/* Background overlay */}
      <View style={styles.overlay} />
      
      {/* Notification content */}
      <View style={styles.notificationContainer}>
        <View style={styles.header}>
          <Text style={styles.headerText}>Cuộc gọi đến</Text>
          <View style={styles.callIcon}>
            <Winicon src="fill/user interface/phone-call" size={16} color="white" />
          </View>
        </View>

        {/* Caller info */}
        <View style={styles.callerInfo}>
          <Animated.View
            style={[
              styles.avatarContainer,
              {
                transform: [{ scale: pulseAnim }],
              },
            ]}
          >
            {callerAvatar ? (
              <FastImage
                source={{ uri: ConfigAPI.urlImg + callerAvatar }}
                style={styles.avatar}
              />
            ) : (
              <View style={[styles.avatar, styles.defaultAvatar]}>
                <Text style={styles.avatarText}>
                  {callerName?.charAt(0).toUpperCase() || '?'}
                </Text>
              </View>
            )}
          </Animated.View>

          <Text style={styles.callerName}>{callerName || 'Người dùng'}</Text>
          <Text style={styles.callerSubtext}>đang gọi cho bạn...</Text>
        </View>

        {/* Action buttons */}
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={onReject}
            activeOpacity={0.8}
          >
            <Winicon src="fill/user interface/phone-call" size={28} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.acceptButton]}
            onPress={onAccept}
            activeOpacity={0.8}
          >
            <Winicon src="fill/user interface/phone-call" size={28} color="white" />
          </TouchableOpacity>
        </View>

        {/* Swipe hint */}
        <Text style={styles.swipeHint}>Vuốt xuống để đóng</Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: height * 0.6,
    zIndex: 9999,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  notificationContainer: {
    flex: 1,
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight || 0,
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  headerText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  callIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  callerInfo: {
    alignItems: 'center',
    marginBottom: 50,
  },
  avatarContainer: {
    marginBottom: 20,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: 'white',
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 48,
    fontWeight: 'bold',
  },
  callerName: {
    color: 'white',
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  callerSubtext: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    textAlign: 'center',
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  actionButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  rejectButton: {
    backgroundColor: '#F44336',
    transform: [{ rotate: '135deg' }],
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  swipeHint: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 20,
  },
});

export default IncomingCallNotification;

import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import IncomingCallOverlayService from '../../services/IncomingCallOverlayService';

// Component để debug IncomingCallNotification
const IncomingCallDebugger: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugInfo = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugInfo(prev => [...prev, `${timestamp}: ${message}`]);
  };

  const clearDebugInfo = () => {
    setDebugInfo([]);
  };

  const testShowIncomingCall = () => {
    addDebugInfo('Testing showIncomingCall...');
    
    const testCallData = {
      callerName: 'Test User ' + Math.floor(Math.random() * 100),
      callerAvatar: undefined,
      callerId: 'test-' + Date.now(),
    };

    addDebugInfo(`Calling showIncomingCall with: ${JSON.stringify(testCallData)}`);
    IncomingCallOverlayService.showIncomingCall(testCallData);
    
    // Check state after call
    const state = IncomingCallOverlayService.getState();
    addDebugInfo(`State after show: ${JSON.stringify(state)}`);
  };

  const testHideIncomingCall = () => {
    addDebugInfo('Testing hideIncomingCall...');
    IncomingCallOverlayService.hideIncomingCall();
    
    const state = IncomingCallOverlayService.getState();
    addDebugInfo(`State after hide: ${JSON.stringify(state)}`);
  };

  const testForceReset = () => {
    addDebugInfo('Testing forceReset...');
    IncomingCallOverlayService.forceReset();
    
    const state = IncomingCallOverlayService.getState();
    addDebugInfo(`State after reset: ${JSON.stringify(state)}`);
  };

  const checkCurrentState = () => {
    const state = IncomingCallOverlayService.getState();
    const listenerCount = IncomingCallOverlayService.listenerCount('showIncomingCall');
    
    addDebugInfo(`Current state: ${JSON.stringify(state)}`);
    addDebugInfo(`Listener count: ${listenerCount}`);
  };

  const testMultipleCalls = () => {
    addDebugInfo('Testing multiple calls...');
    
    // Call 1
    setTimeout(() => {
      addDebugInfo('Showing call 1');
      IncomingCallOverlayService.showIncomingCall({
        callerName: 'Caller 1',
        callerId: 'caller-1',
      });
    }, 100);

    // Call 2 (should replace call 1)
    setTimeout(() => {
      addDebugInfo('Showing call 2');
      IncomingCallOverlayService.showIncomingCall({
        callerName: 'Caller 2', 
        callerId: 'caller-2',
      });
    }, 2000);

    // Hide after 4 seconds
    setTimeout(() => {
      addDebugInfo('Hiding call');
      IncomingCallOverlayService.hideIncomingCall();
    }, 4000);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>IncomingCall Debugger</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={testShowIncomingCall}>
          <Text style={styles.buttonText}>Test Show Call</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testHideIncomingCall}>
          <Text style={styles.buttonText}>Test Hide Call</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testForceReset}>
          <Text style={styles.buttonText}>Force Reset</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={checkCurrentState}>
          <Text style={styles.buttonText}>Check State</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testMultipleCalls}>
          <Text style={styles.buttonText}>Test Multiple Calls</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearDebugInfo}>
          <Text style={styles.buttonText}>Clear Debug</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.debugContainer}>
        <Text style={styles.debugTitle}>Debug Log:</Text>
        {debugInfo.map((info, index) => (
          <Text key={index} style={styles.debugText}>
            {info}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  clearButton: {
    backgroundColor: '#FF9800',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  debugContainer: {
    flex: 1,
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    maxHeight: 400,
  },
  debugTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  debugText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 2,
    lineHeight: 16,
  },
});

export default IncomingCallDebugger;

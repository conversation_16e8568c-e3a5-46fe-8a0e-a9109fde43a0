import { Alert } from 'react-native';
import { navigate, RootScreen } from '../router/router';
import WebRTCService from '../features/call/WebRTCService';
import { showSnackbar, ComponentStatus } from 'wini-mobile-components';

interface IncomingCallData {
  from: string;
  fromName?: string;
}

class CallNotificationService {
  private static instance: CallNotificationService;
  private isInitialized = false;

  private constructor() {}

  static getInstance(): CallNotificationService {
    if (!CallNotificationService.instance) {
      CallNotificationService.instance = new CallNotificationService();
    }
    return CallNotificationService.instance;
  }

  initialize() {
    if (this.isInitialized) {
      return;
    }

    // Setup WebRTC socket listeners
    WebRTCService.setupSocketListeners();

    // Thiết lập callback cho WebRTC để xử lý incoming calls
    WebRTCService.setCallbacks({
      onCallReceived: this.handleIncomingCall.bind(this),
      onCallAccepted: this.handleCallAccepted.bind(this),
      onCallRejected: this.handleCallRejected.bind(this),
      onCallEnded: this.handleCallEnded.bind(this),
      onError: this.handleCallError.bind(this),
    });

    this.isInitialized = true;
    console.log('✅ CallNotificationService initialized');
  }

  private handleIncomingCall(data: IncomingCallData) {
    console.log('📞 Incoming call from:', data.from);

    // Hiển thị alert cho cuộc gọi đến
    Alert.alert(
      'Cuộc gọi đến',
      `${data.fromName || 'Người dùng'} đang gọi cho bạn`,
      [
        {
          text: 'Từ chối',
          style: 'cancel',
          onPress: () => {
            WebRTCService.rejectCall();
          },
        },
        {
          text: 'Chấp nhận',
          onPress: () => {
            this.acceptIncomingCall(data);
          },
        },
      ],
      { cancelable: false }
    );
  }

  private async acceptIncomingCall(data: IncomingCallData) {
    try {
      // Navigate đến CallScreen trước
      navigate(RootScreen.CallScreen, {
        isIncoming: true,
        callerId: data.from,
        callerName: data.fromName,
      });

      // Sau đó chấp nhận cuộc gọi
      await WebRTCService.acceptCall();
    } catch (error) {
      console.error('Error accepting incoming call:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể chấp nhận cuộc gọi',
      });
    }
  }

  private handleCallAccepted(data: { from: string }) {
    console.log('✅ Call accepted by:', data.from);
    showSnackbar({
      status: ComponentStatus.SUCCESS,
      message: 'Cuộc gọi đã được chấp nhận',
    });
  }

  private handleCallRejected(data: { from: string }) {
    console.log('❌ Call rejected by:', data.from);
    showSnackbar({
      status: ComponentStatus.WARNING,
      message: 'Cuộc gọi bị từ chối',
    });
  }

  private handleCallEnded(data: { from: string }) {
    console.log('📞 Call ended by:', data.from);
    showSnackbar({
      status: ComponentStatus.INFOR,
      message: 'Cuộc gọi đã kết thúc',
    });
  }

  private handleCallError(error: any) {
    console.error('📞 Call error:', error);
    showSnackbar({
      status: ComponentStatus.ERROR,
      message: 'Có lỗi xảy ra trong cuộc gọi',
    });
  }

  // Phương thức để hiển thị notification khi app ở background
  showIncomingCallNotification(data: IncomingCallData) {
    // TODO: Implement push notification for incoming calls when app is in background
    // Có thể sử dụng react-native-push-notification hoặc @react-native-firebase/messaging
    console.log('📱 Should show push notification for incoming call:', data);
  }

  // Cleanup khi app bị đóng
  cleanup() {
    this.isInitialized = false;
    console.log('🧹 CallNotificationService cleaned up');
  }
}

export default CallNotificationService.getInstance();

import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { AppSvg, ComponentStatus, showSnackbar } from 'wini-mobile-components';
import { TextFieldForm } from '../../../modules/news/form/component-form';
import { ColorThemes } from '../../../assets/skin/colors';
import { DataController } from '../../../base/baseController';
import { useNavigation } from '@react-navigation/native';
import { useSelectorShopState } from '../../../redux/hook/shopHook ';

const { width } = Dimensions.get('window');

const ModalPromotion = ({
  isShow,
  closeModal,
  svgSrc,
  title,
  dataDiscount,
  handleSelectAllData,
  ref,
}: {
  isShow: boolean;
  closeModal: () => void;
  svgSrc: string;
  title: string;
  dataDiscount: any[];
  handleSelectAllData: (ref: any) => void;
  ref: any;
}) => {
  const [DiscountValue, setDiscountValue] = useState<any[]>([]);
  const [errText, setErrText] = useState<string>('');
  const shopInfo = useSelectorShopState().data;

  const productDA = new DataController('Product');

  const methods = useForm({ shouldFocusError: false });
  let textFieldStyle = {
    height: 48,
    paddingLeft: 8,
    paddingRight: 8,
    borderWidth: 0,
  };

  const fetchProducts = async () => {
    if (isShow && dataDiscount && dataDiscount?.length > 0) {
      try {
        // Extract category IDs from dataDiscount (including children)
        const categoryIds: string[] = [];
        dataDiscount.forEach(item => {
          // Add parent category ID
          if (item.Id) {
            categoryIds.push(item.Id);
          }
          // Add children category IDs
          if (item.Children && Array.isArray(item.Children)) {
            item.Children.forEach((child: any) => {
              if (child.Id) {
                categoryIds.push(child.Id);
              }
            });
          }
        });
        // Remove duplicates and filter out null/undefined values
        const uniqueCategoryIds = [...new Set(categoryIds)].filter(id => id);
        console.log('check-categoryIds', uniqueCategoryIds);
        if (uniqueCategoryIds.length > 0) {
          // Fetch products based on criteria
          const categoryQuery = uniqueCategoryIds.join(' | ');
          const shopId = shopInfo[0]?.Id;

          const response = await productDA.getListSimple({
            query: `@ShopId:{${shopId}} @CategoryId:{${categoryQuery}}`,
            page: 1,
            size: 1000 // Adjust size as needed
          });
          console.log('check-fetchedProducts', response);

          if (response?.code === 200 && response?.data) {
            // Filter products with no discount or discount = 0
            const filteredProducts = response.data.filter((product: any) => {
              return !product.Discount || product.Discount === 0;
            });
            setDiscountValue(filteredProducts);
          } else {
            setDiscountValue([]);
          }
        } else {
          setDiscountValue([]);
        }
      } catch (error) {
        console.error('Error fetching products:', error);
        setDiscountValue([]);
      }
    } else {
      setDiscountValue([]);
    }
  };
  useEffect(() => {
    console.log('check-dataDiscount', dataDiscount);
    if (dataDiscount && dataDiscount?.length > 0 && (dataDiscount[0]?.Price || dataDiscount[0]?.InStock)) {
      setDiscountValue(dataDiscount);
    } else {
      fetchProducts();
    }
  }, [isShow, dataDiscount]);

  useEffect(() => {
    if (Number(methods.watch('Content')) > 100) {
      setErrText('Số % không được lớn hơn 100');
      return;
    } else {
      setErrText('');
    }
  }, [methods.watch('Content')]);

  const onSubmit = async (data: any) => {
    if (DiscountValue && DiscountValue?.length === 0) {
      closeModal();
      showSnackbar({
        message: 'không có sản phẩm nào theo danh mục bạn đã chọn',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    try {
      // Update all products with the new discount value
      const updatedDiscountValue = DiscountValue.map(item => ({
        ...item,
        Discount: Number(data.Content),
        // Convert Children to string if it exists
        ...(item.Children && { Children: item.Children.toString() })
      }));

      console.log('check-updatedDiscountValue', updatedDiscountValue);

      const response = await productDA.edit(updatedDiscountValue);
      console.log('check-response', response);

      if (response?.code === 200) {
        closeModal();
        methods.setValue('Content', '');
        setErrText('');
        handleSelectAllData(ref);
        showSnackbar({
          message: 'Cập nhật giảm giá thành công',
          status: ComponentStatus.SUCCSESS,
        });
      } else {
        showSnackbar({
          message: 'Có lỗi xảy ra khi cập nhật giảm giá',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error updating discount:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi cập nhật giảm giá',
        status: ComponentStatus.ERROR,
      });
    }
  };

  return (
    <View style={styles.container}>
      {/* Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={isShow}
        onRequestClose={closeModal}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            {/* Icon cảnh báo */}
            <AppSvg SvgSrc={svgSrc} size={60} />
            {/* Nội dung thông báo */}
            <Text style={styles.modalText}>{title}</Text>
            <View style={{ width: '100%' }}>
              <Text style={{ fontSize: 16, fontWeight: '600' }}>Nhập số %</Text>
              <View>
                <TextFieldForm
                  control={methods.control}
                  name="Content"
                  placeholder="Nhập số % đánh giá"
                  returnKeyType="done"
                  textFieldStyle={textFieldStyle}
                  errors={methods.formState.errors}
                  type="number-pad"
                  register={methods.register}
                  required
                  style={styles.input}
                />
              </View>
            </View>
            {errText && <Text style={{ color: 'red' }}>{errText}</Text>}
            <View style={styles.buttonContainer}>
              {/* Nút Hủy */}
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={closeModal}
                activeOpacity={0.8}>
                <Text style={styles.cancelButtonText}>Đóng</Text>
              </TouchableOpacity>

              {/* Nút Đồng ý */}
              <TouchableOpacity
                style={[styles.button, styles.agreeButton]}
                onPress={errText ? undefined : methods.handleSubmit(onSubmit)}
                activeOpacity={0.8}>
                <Text style={styles.agreeButtonText}>Đồng ý</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  showButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  showButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    width: width * 0.8,
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  warningIcon: {
    width: 50,
    height: 50,
    backgroundColor: '#FF8C00',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  warningIconText: {
    color: 'white',
    fontSize: 28,
    fontWeight: 'bold',
  },
  modalText: {
    fontSize: 20,
    marginTop: 12,
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: 12,
    marginTop: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    color: 'black',
  },
  cancelButton: {
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  agreeButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  cancelButtonText: {
    color: ColorThemes.light.neutral_text_title_color,
    fontSize: 16,
    fontWeight: '600',
  },
  agreeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  input: {
    borderRadius: 10,
    fontSize: 16,
    color: '#000',
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: ColorThemes.light.neutral_main_border_color,
    marginBottom: 16,
    marginTop: 4,
  },
});

export default ModalPromotion;

# Debug Accept Call Issues

## 🐛 **Current Issues:**
1. ❌ Timer không start sau khi accept call
2. ❌ Controls không show mic/speaker/end call buttons
3. ❌ Stuck ở incoming call state

## 🔍 **Debug Steps Added:**

### **1. Enhanced State Logging:**
```typescript
console.log('📞 CallScreen received state change:', JSON.stringify(state, null, 2));
console.log('📞 Timer conditions:', {
  hasTimer: !!callTimerRef.current,
  isInCall: state.isInCall,
  isConnected: state.isConnected,
  isIncoming: state.isIncoming
});
```

### **2. Timer Debug:**
```typescript
const startCallTimer = () => {
  console.log('📞 startCallTimer() called');
  console.log('📞 Starting new timer interval');
  callTimerRef.current = setInterval(() => {
    setCallDuration(prev => {
      const newDuration = prev + 1;
      console.log('📞 Timer tick:', newDuration);
      return newDuration;
    });
  }, 1000);
};
```

### **3. Accept Call Debug:**
```typescript
const handleAcceptCall = async () => {
  console.log('📞 handleAcceptCall called');
  console.log('📞 Calling WebRTCService.acceptCall()');
  await WebRTCService.acceptCall();
  console.log('📞 WebRTCService.acceptCall() completed');
  
  // Force start timer as backup
  setTimeout(() => {
    if (!callTimerRef.current) {
      console.log('📞 Force starting timer after accept');
      startCallTimer();
    }
  }, 1000);
};
```

### **4. Visual Debug Info:**
```typescript
<Text style={{ color: 'white', fontSize: 12, textAlign: 'center', marginBottom: 10 }}>
  Debug: isIncoming={callState.isIncoming ? 'true' : 'false'}, isConnected={callState.isConnected ? 'true' : 'false'}
</Text>
```

### **5. Multiple Timer Triggers:**
- ✅ `onCallStateChanged` when `isConnected = true`
- ✅ `onCallAccepted` callback
- ✅ `handleAcceptCall` with 1s delay backup

## 📱 **Test Flow:**

### **Step 1: Trigger Incoming Call**
```typescript
IncomingCallOverlayService.showIncomingCall({
  callerName: 'Test User',
  callerId: 'test-123',
});
```

### **Step 2: Accept Call**
1. Tap Accept in FloatingIncomingCall
2. Watch console logs
3. Check CallScreen UI

### **Expected Console Logs:**
```
📞 User tapped Accept button, callData: {...}
📞 handleAcceptCall called
📞 Calling WebRTCService.acceptCall()
📞 WebRTC State Update: {
  from: { isIncoming: true, isConnected: false },
  to: { isIncoming: false, isConnected: true },
  updates: { isIncoming: false, isConnected: true, callStartTime: ... }
}
📞 CallScreen received state change: {
  "isInCall": true,
  "isIncoming": false,
  "isConnected": true,
  "isOutgoing": false,
  ...
}
📞 Timer conditions: {
  hasTimer: false,
  isInCall: true,
  isConnected: true,
  isIncoming: false
}
📞 ✅ Starting timer - all conditions met
📞 startCallTimer() called
📞 Starting new timer interval
📞 Call accepted callback triggered
📞 Timer tick: 1
📞 Timer tick: 2
📞 Timer tick: 3
```

### **Expected UI:**
- ✅ Debug text shows: `isIncoming=false, isConnected=true`
- ✅ Shows mic, speaker, end call buttons (NOT accept/reject)
- ✅ Timer counts up: 00:01, 00:02, 00:03...
- ✅ Status shows timer duration

## 🚨 **If Still Not Working:**

### **Check These:**
1. **WebRTC State**: Is `isConnected` actually being set to `true`?
2. **Timer Conditions**: Are all conditions met for timer start?
3. **Controls Logic**: Is `isIncoming` properly set to `false`?
4. **Callback Setup**: Are WebRTC callbacks properly registered?

### **Manual Debug:**
```typescript
// In CallScreen, add manual trigger
useEffect(() => {
  // Manual test after 3 seconds
  setTimeout(() => {
    console.log('📞 Manual timer test');
    if (!callTimerRef.current) {
      startCallTimer();
    }
  }, 3000);
}, []);
```

### **Force State Update:**
```typescript
// In handleAcceptCall, force state
setTimeout(() => {
  setCallState(prev => ({
    ...prev,
    isIncoming: false,
    isConnected: true,
  }));
}, 500);
```

## 🎯 **Success Criteria:**

### **✅ Timer Working:**
- Console shows timer ticks every second
- UI shows counting duration (00:01, 00:02...)
- Status text shows timer instead of "Cuộc gọi bắt đầu"

### **✅ Controls Working:**
- Debug text shows `isIncoming=false`
- Shows mic, speaker, end call buttons
- Does NOT show accept/reject buttons

### **✅ State Working:**
- WebRTC state updates properly
- CallScreen receives correct state
- All conditions met for timer start

## 🔧 **Quick Fixes to Try:**

### **1. Force Timer Start:**
```typescript
// Add to handleAcceptCall
setTimeout(() => {
  console.log('📞 Force starting timer');
  startCallTimer();
}, 2000);
```

### **2. Force State Update:**
```typescript
// Add to handleAcceptCall
setCallState({
  isInCall: true,
  isIncoming: false,
  isConnected: true,
  isOutgoing: false,
});
```

### **3. Simplify Timer Condition:**
```typescript
// Change timer condition to just:
if (!callTimerRef.current && state.isInCall) {
  startCallTimer();
}
```

**Test với debug logs này để xem vấn đề ở đâu!** 🔍

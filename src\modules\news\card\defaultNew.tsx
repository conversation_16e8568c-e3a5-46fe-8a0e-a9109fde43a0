/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {App<PERSON>utton, Winicon} from 'wini-mobile-components';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import RenderHTML from 'react-native-render-html';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
import HTMLProcessor from '../../../utils/convert';
interface cardItem {
  Id: string;
  Name: string;
  Description?: string;
  Img: string;
  Content?: string;
  relativeUser?: {image?: string; title?: string};
}

interface Props {
  containerStyle?: ViewStyle;
  mainContainerStyle?: ViewStyle;
  flexDirection?: 'default' | 'row';
  imgStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  data: cardItem;
  listItems?: Array<any>;
  listTags?: Array<any>;
  onPressSeeMore?: () => void;
  onPressLikeAction?: () => void;
  onPressDetail?: () => void;
  reportContent?: React.ReactNode;
  actionView?: React.ReactNode;
  horizontalList?: boolean;
  noDivider?: boolean;
  showContent?: boolean;
  dividerColor?: string;
}

export function DefaultNew(props: Props) {
  return (
    <TouchableOpacity
      onPress={props.onPressDetail}
      disabled={props.onPressDetail ? false : true}
      style={[
        stylesDefault.touchableContainer,
        {
          flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
          width: Dimensions.get('screen').width - 32,
        },
        props.containerStyle,
      ]}>
      <View
        style={[
          stylesDefault.mainContainer,
          {
            flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
          },
          props.mainContainerStyle,
        ]}>
        <View
          style={[
            stylesDefault.img,
            props.flexDirection === 'row'
              ? stylesDefault.imageContainerRow
              : {
                  ...stylesDefault.imageContainer,
                  width: Dimensions.get('screen').width - 32,
                },
            props.imgStyle,
          ]}>
          <Image
            source={{
              uri: props?.data.Img
                ? `${props?.data.Img}`
                : 'https:placehold.co/88/FFFFFF/000000/png',
            }}
            style={stylesDefault.absoluteImage}
            resizeMode="cover"
          />
          {props.flexDirection === 'default' ? (
            <AppButton
              backgroundColor={'transparent'}
              borderColor="transparent"
              onPress={() => {
                if (props.onPressLikeAction) {
                  props.onPressLikeAction();
                }
              }}
              containerStyle={stylesDefault.likeButton}
              title={<Winicon src="outline/emoticons/heart" size={15} />}
            />
          ) : null}
        </View>
        <View
          style={[
            props.flexDirection === 'row'
              ? stylesDefault.dividerContainerRow
              : stylesDefault.dividerContainer,
            {
              borderBottomColor:
                props.dividerColor ??
                ColorThemes.light.neutral_main_border_color,
              borderBottomWidth: props.noDivider ? 0 : 1,
            },
          ]}>
          <View style={[stylesDefault.mainContent]}>
            {/* infor on top */}
            {props.data.relativeUser ? (
              <View style={stylesDefault.relativeUserContainer}>
                {/* infor img */}
                <View style={stylesDefault.relativeUserImage}>
                  <Image
                    source={{
                      uri: props.data.relativeUser.image
                        ? `${ConfigAPI.urlImg + props?.data.Img}`
                        : 'https://reactnative.dev/img/tiny_logo.png',
                    }}
                    style={stylesDefault.relativeUserImageStyle}
                  />
                </View>
                {/* infor text */}
                <View style={stylesDefault.relativeUserTextContainer}>
                  <Text style={[stylesDefault.inforTitle]}>
                    {props.data.relativeUser.title ?? ''}
                  </Text>
                </View>
              </View>
            ) : null}
            {/* title */}
            {props.data.Name && typeof props.data.Name === 'string' ? (
              <Text
                style={[
                  stylesDefault.titleStyle,
                  {
                    paddingBottom: props.data.Description ? 4 : 0,
                  },
                  props.titleStyle,
                ]}
                numberOfLines={2}>
                {props.data.Name ?? ''}
              </Text>
            ) : null}
            {/* subtitle */}
            {props.data?.Description ? (
              <View style={stylesDefault.descriptionContainer}>
                <Text
                  style={[stylesDefault.subTitleStyle, props.subtitleStyle]}
                  numberOfLines={props.showContent ? undefined : 2}>
                  {props.data.Description ?? ''}
                </Text>
              </View>
            ) : null}
            {props.data?.Content && props.showContent ? (
              <View
                style={
                  props.showContent
                    ? stylesDefault.contentContainer
                    : stylesDefault.contentContainerWithHeight
                }>
                <Text
                  style={[stylesDefault.bodyContentStyle]}
                  numberOfLines={3}>
                  {props.data.Content.includes('<')
                    ? HTMLProcessor.processHTMLContent(props.data.Content ?? '')
                    : props.data.Content ?? ''}
                </Text>
              </View>
            ) : null}
            {props.listItems?.length ? (
              <View style={stylesDefault.listItemsContainer}>
                {props.listItems.map((item, index) => {
                  return (
                    <View key={item.Id} style={stylesDefault.listItem}>
                      <View style={stylesDefault.listItemIcon}>
                        {item.icon ? (
                          <Winicon src={item.icon} size={16} />
                        ) : (
                          <Text style={[stylesDefault.inforTitle]}>*</Text>
                        )}
                      </View>
                      <Text
                        style={[
                          stylesDefault.inforTitle,
                          stylesDefault.listItemTitle,
                        ]}>
                        {item.title}
                      </Text>
                    </View>
                  );
                })}
              </View>
            ) : null}
            {props.onPressSeeMore && props.listItems?.length ? (
              <AppButton
                title={'See more'}
                containerStyle={stylesDefault.seeMoreButton}
                backgroundColor={'transparent'}
                textStyle={TypoSkin.buttonText3}
                borderColor="transparent"
                suffixIconSize={16}
                suffixIcon={'outline/arrows/circle-arrow-right'}
                onPress={props.onPressSeeMore}
                textColor={ColorThemes.light.infor_main_color}
              />
            ) : null}
            {props.listTags?.length ? (
              <View style={stylesDefault.tagsContainer}>
                {props.listTags.map((item, index) => {
                  return (
                    <View key={item.Id} style={stylesDefault.tagItem}>
                      <Text style={[stylesDefault.inforTitle]}>
                        {item.title}
                      </Text>
                      <Winicon src={'outline/arrows/right-arrow'} size={12} />
                    </View>
                  );
                })}
              </View>
            ) : null}
            {props.flexDirection === 'default' && props.reportContent && (
              <View style={stylesDefault.reportContentContainer}>
                <View style={stylesDefault.reportContentInner}>
                  {props.reportContent ? props.reportContent : null}
                </View>
              </View>
            )}
            <View style={stylesDefault.actionViewContainer}>
              {props.actionView ? props.actionView : null}
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}

export function SkeletonPlaceCard() {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View
        style={[
          stylesDefault.mainContainer,
          stylesDefault.skeletonMainContainer,
        ]}>
        <View style={stylesDefault.img} />
        <View style={stylesDefault.skeletonFlexContainer}>
          <View style={stylesDefault.skeletonTitle} />
          <View style={stylesDefault.skeletonSubtitle} />
        </View>
      </View>
      <View style={stylesDefault.mainContent}>
        <View style={stylesDefault.skeletonContent} />
        <View style={stylesDefault.skeletonContentShort} />
      </View>
    </SkeletonPlaceholder>
  );
}

const stylesDefault = StyleSheet.create({
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  inforTitle: {
    fontSize: 12,
    color: '#61616B',
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
  touchableContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    alignSelf: 'flex-start',
    flex: 1,
  },
  imageContainer: {
    height: 234,
  },
  imageContainerRow: {
    height: 88,
    width: 88,
  },
  absoluteImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    objectFit: 'cover',
    position: 'absolute',
  },
  likeButton: {
    borderRadius: 100,
    padding: 6,
    height: 24,
    width: 24,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    zIndex: 1,
    position: 'absolute',
    right: 16,
    top: 16,
  },
  dividerContainer: {
    paddingBottom: 8,
    alignSelf: 'flex-start',
  },
  dividerContainerRow: {
    paddingBottom: 8,
    flexDirection: 'row',
    alignSelf: 'flex-start',
    maxWidth: '80%',
  },
  relativeUserContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 4,
    marginBottom: 4,
  },
  relativeUserImage: {
    width: 20,
    height: 20,
    borderRadius: 100,
    backgroundColor: 'black',
  },
  relativeUserImageStyle: {
    width: '100%',
    height: '100%',
    borderRadius: 100,
  },
  relativeUserTextContainer: {
    flex: 1,
  },
  descriptionContainer: {
    paddingTop: 4,
    paddingBottom: 8,
  },
  contentContainer: {
    overflow: 'hidden',
  },
  contentContainerWithHeight: {
    height: 65,
    overflow: 'hidden',
  },
  listItemsContainer: {
    width: '100%',
    paddingTop: 16,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingVertical: 4,
    gap: 8,
  },
  listItemIcon: {
    width: 16,
    borderRadius: 100,
  },
  listItemTitle: {
    color: '#313135',
  },
  seeMoreButton: {
    justifyContent: 'flex-start',
    alignSelf: 'baseline',
    marginVertical: 8,
  },
  tagsContainer: {
    width: '100%',
    flexDirection: 'row',
    gap: 8,
  },
  tagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    paddingHorizontal: 8,
    borderRadius: 24,
    borderColor: ColorThemes.light.neutral_bolder_border_color,
    borderWidth: 1,
    paddingVertical: 4,
    gap: 4,
  },
  reportContentContainer: {
    height: 96,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    marginVertical: 16,
    padding: 24,
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  reportContentInner: {
    gap: 4,
  },
  actionViewContainer: {
    flex: 1,
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
  },
  // Skeleton styles
  skeletonMainContainer: {
    marginBottom: 16,
  },
  skeletonFlexContainer: {
    flex: 1,
  },
  skeletonTitle: {
    width: '60%',
    height: 16,
    borderRadius: 4,
    marginBottom: 6,
    backgroundColor: '#e0e0e0',
  },
  skeletonSubtitle: {
    width: '40%',
    height: 12,
    borderRadius: 4,
    backgroundColor: '#e0e0e0',
  },
  skeletonContent: {
    width: '100%',
    height: 16,
    borderRadius: 4,
    marginBottom: 6,
    backgroundColor: '#e0e0e0',
  },
  skeletonContentShort: {
    width: '80%',
    height: 16,
    borderRadius: 4,
    backgroundColor: '#e0e0e0',
  },
});

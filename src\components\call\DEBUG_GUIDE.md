# IncomingCallNotification Debug Guide

## 🐛 **Vấn đề:**
IncomingCallNotification chỉ hiển thị 1 lần khi mới mở app, c<PERSON><PERSON> lần call tiếp theo không bật lên nữa.

## 🔍 **Nguy<PERSON><PERSON> nhân có thể:**

### **1. State không được reset đúng cách**
- `showNotification` state stuck ở `true`
- `callData` state không được clear
- `isShowing` trong service không reset

### **2. Event listeners bị duplicate hoặc không hoạt động**
- Multiple listeners được add mà không remove
- Listeners bị override
- Event không được emit đúng cách

### **3. Modal/Component lifecycle issues**
- Modal không unmount đúng cách
- Component state conflicts
- React re-render issues

## ✅ **Các fix đã implement:**

### **1. Force State Reset**
```typescript
// Trong handleShowIncomingCall
setShowNotification(false);
setCallData(null);

setTimeout(() => {
  setCallData(data);
  setShowNotification(true);
}, 100);
```

### **2. Prevent Duplicate Listeners**
```typescript
// Clear existing listeners trước khi add mới
IncomingCallOverlayService.off('showIncomingCall', handleShowIncomingCall);
// ... clear other listeners

// Then add new listeners
IncomingCallOverlayService.on('showIncomingCall', handleShowIncomingCall);
```

### **3. Service State Management**
```typescript
// Trong IncomingCallOverlayService
if (this.isShowing) {
  console.log('Notification already showing, hiding first');
  this.hideIncomingCall();
  
  setTimeout(() => {
    this.showIncomingCall(callData);
  }, 200);
  return;
}
```

### **4. Enhanced Debug Logging**
```typescript
// Track state changes
console.log('📞 Current notification state:', { showNotification, callData });
console.log('📞 Service state:', IncomingCallOverlayService.getState());
console.log('📞 Listener count:', IncomingCallOverlayService.listenerCount('showIncomingCall'));
```

## 🧪 **Debug Steps:**

### **Step 1: Check Console Logs**
Khi có incoming call, check console logs:
```
📞 IncomingCallOverlayService.showIncomingCall called with: {...}
📞 Current isShowing state: false
📞 EventEmitter listeners count: 1
📞 handleShowIncomingCall received data: {...}
📞 Current notification state before show: {...}
📞 Notification shown with new data
📞 Modal onShow triggered
```

### **Step 2: Use Debug Component**
```typescript
import IncomingCallDebugger from '../components/call/IncomingCallDebugger';

// Render component để test
<IncomingCallDebugger />
```

### **Step 3: Manual Testing**
```typescript
// Test trong console hoặc debug component
IncomingCallOverlayService.showIncomingCall({
  callerName: 'Test User',
  callerId: 'test-123',
});

// Check state
console.log(IncomingCallOverlayService.getState());
```

### **Step 4: Force Reset if Stuck**
```typescript
// Nếu notification bị stuck
IncomingCallOverlayService.forceReset();
```

## 📱 **Expected Flow:**

### **First Call (Working):**
1. `showIncomingCall()` called
2. Service `isShowing = false` → set to `true`
3. Emit `showIncomingCall` event
4. Provider receives event → `setShowNotification(true)`
5. Modal shows with notification

### **Second Call (Should work now):**
1. `showIncomingCall()` called
2. Service `isShowing = true` → call `hideIncomingCall()` first
3. After 200ms delay → call `showIncomingCall()` again
4. Service `isShowing = false` → set to `true`
5. Emit event → Provider shows notification

### **After Call Ends:**
1. `handleHideIncomingCall()` called
2. Service `isShowing = true` → set to `false`
3. Provider `setShowNotification(false)`
4. Modal hides
5. Ready for next call

## 🔧 **Debug Commands:**

### **Check Current State:**
```typescript
// Service state
console.log('Service state:', IncomingCallOverlayService.getState());

// Listener count
console.log('Listeners:', IncomingCallOverlayService.listenerCount('showIncomingCall'));

// Provider state (add to component)
console.log('Provider state:', { showNotification, callData });
```

### **Force Reset:**
```typescript
// Reset service
IncomingCallOverlayService.forceReset();

// Reset provider (add to component)
setShowNotification(false);
setCallData(null);
```

### **Test Multiple Calls:**
```typescript
// Call 1
IncomingCallOverlayService.showIncomingCall({
  callerName: 'Caller 1',
  callerId: 'caller-1',
});

// Wait 2 seconds, then call 2
setTimeout(() => {
  IncomingCallOverlayService.showIncomingCall({
    callerName: 'Caller 2',
    callerId: 'caller-2',
  });
}, 2000);
```

## 🎯 **Success Criteria:**

- ✅ **First call**: Shows notification correctly
- ✅ **Second call**: Shows notification correctly (replaces first)
- ✅ **After hide**: State is properly reset
- ✅ **Multiple calls**: Each call shows notification
- ✅ **Console logs**: Show proper state transitions
- ✅ **No stuck state**: Notification never gets stuck

## 🚨 **Common Issues & Solutions:**

### **Issue: Notification stuck showing**
```typescript
// Solution: Force reset
IncomingCallOverlayService.forceReset();
```

### **Issue: No listeners**
```typescript
// Solution: Check listener count
console.log('Listeners:', IncomingCallOverlayService.listenerCount('showIncomingCall'));

// Re-register if needed
IncomingCallOverlayService.on('showIncomingCall', handleShowIncomingCall);
```

### **Issue: State not updating**
```typescript
// Solution: Force state update
setShowNotification(false);
setTimeout(() => setShowNotification(true), 100);
```

## 🎉 **Expected Result:**

After implementing these fixes:
- ✅ **First call works**
- ✅ **Second call works** 
- ✅ **Third call works**
- ✅ **All subsequent calls work**
- ✅ **No stuck notifications**
- ✅ **Proper state management**

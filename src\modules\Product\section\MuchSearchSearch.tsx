import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import ProductItem from '../FavoriteProductComponent/ProductItem';
import {productAction} from '../../../redux/actions/productAction';
import {Product} from '../../../redux/models/product';
import {getRandomObjects} from '../../../utils/arrayUtils';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';

// Header Component
interface HeaderProps {
  title: string;
  onSeeMore?: () => void;
}

const Header: React.FC<HeaderProps> = ({title, onSeeMore}) => (
  <View style={styles.header}>
    <Text style={styles.headerTitle}>{title}</Text>
    <TouchableOpacity onPress={onSeeMore}>
      <Text style={styles.seeMore}>Xem thêm</Text>
    </TouchableOpacity>
  </View>
);

// Main Screen Component
const MostSearchedScreen: React.FC<{
  onSeeMore: () => void;
  onRefresh: boolean;
}> = ({onSeeMore, onRefresh}) => {
  const [products, setProducts] = useState<Product[]>([]);

  useEffect(() => {
    getData();
  }, [onRefresh]);

  const getData = async () => {
    let data = await productAction.find({
      page: 1,
      size: 100,
    });
    if (data.length > 10) {
      data = getRandomObjects(data, 5);
    }
    setProducts(data);
  };

  const handleSeeMore = useCallback(() => {
    onSeeMore();
  }, [onSeeMore]);

  return (
    <View style={styles.screen}>
      <StatusBar barStyle="dark-content" />
      <Header title="Được tìm kiếm nhiều" onSeeMore={handleSeeMore} />
      {products?.length !== 0 &&
        products.map((item: any) => <ProductItem key={item.Id} item={item} />)}
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  screen: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
  },
  headerTitle: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  seeMore: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.infor_main_color,
  },
  separator: {
    height: 1,
    backgroundColor: '#EAEAEA',
    marginLeft: 96,
  },
});

export default MostSearchedScreen;

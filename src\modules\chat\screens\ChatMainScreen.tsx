import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import { navigate, RootScreen } from '../../../router/router';
import { useSocketStatus } from '../../../hooks/useSocketConnection';
import ChatListScreen from './ChatListScreen';
import ContactsScreen from './ContactsScreen';
import CallHistoryScreen from './CallHistoryScreen';
import HeaderLogo from '../../../Screen/Layout/headers/HeaderLogo';
import SocketDebugInfo from '../components/SocketDebugInfo';
import { DataController } from '../../../base/baseController';
import store from '../../../redux/store/store';
import { useNavigation } from '@react-navigation/native';
import { Winicon } from 'wini-mobile-components';

const ChatMainScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'messages' | 'contacts' | 'calls'>('messages');
  const { isConnected, connectionStatus } = useSocketStatus();
  const navigation = useNavigation<any>();
  const [tabs, setTabs] = useState([
    {
      id: 'messages',
      title: 'Tin nhắn',
      icon: 'fill/social media/logo-messenger',
      badge: 0, // Số tin nhắn chưa đọc
    },
    {
      id: 'contacts',
      title: 'Danh bạ',
      icon: 'color/sport/users-mm',
      
    },
    {
      id: 'calls',
      title: 'Lịch sử cuộc gọi',
      icon: 'fill/user interface/phone-call',
    },
  ]);
  useEffect(() => {
    getMessageUnreadCount().then((count) => {
      setTabs((prevTabs) => {
        return prevTabs.map((tab) => {
          if (tab.id === 'messages') {
            return { ...tab, badge: count };
          }
          return tab;
        });
      });
    });
  }, []);
  //focus effect
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', async () => {
      const count = await getMessageUnreadCount();
      setTabs((prevTabs) => {
        return prevTabs.map((tab) => {
          if (tab.id === 'messages') {
            return { ...tab, badge: count };
          }
          return tab;
        });
      });
    });
    return unsubscribe;
  }, [navigation]);
  const getMessageUnreadCount = async () => {
    const messageController = new DataController('Message');
    const chatRoomController = new DataController('ChatRoom');
    const userId = store.getState().customer.data?.Id;
    //lấy tất cả room chat 
   await chatRoomController.getListSimple({
      query: `@CustomerId:{${userId}} | @Members:(*${userId}*)`,
      size: 0,
      returns: ['Id'],
    }).then((response) => {
      if (response.code === 200) {
        const roomIds = response.data.map((item: any) => item.Id);
        //lấy tất cả tin nhắn trong các room chat
        messageController.getListSimple({
          query: `@ChatRoomId:{${roomIds.join(' | ')}} -@CustomerId: {${userId}} @isRead: {false}`,
          returns: ['Id'],
        }).then((response) => {
          if (response.code === 200) {
            return response.totalCount;
          }
          return 0;
        });
      }
      return 0;
    });
    
    return 0;
  };
  const renderTabContent = () => {
    switch (activeTab) {
      case 'messages':
        return <ChatListScreen />;
      case 'contacts':
        return <ContactsScreen />;
      case 'calls':
        return <CallHistoryScreen />;
      default:
        return <ChatListScreen />;
    }
  };

  const renderTabBar = () => (
    <View style={styles.tabContainer}>
      {/* Search bar */}
      <View style={styles.searchContainer}>
        <TouchableOpacity style={styles.searchBar} activeOpacity={0.8}>
          <Text style={styles.searchPlaceholder}>Bạn muốn tìm gì?</Text>
        </TouchableOpacity>
      </View>

      {/* Tab buttons */}
      <View style={styles.tabBar}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={{...styles.tabButton, width: tab.id === 'calls' ? '40%' : '30%'}}
            onPress={() => setActiveTab(tab.id as any)}
            activeOpacity={0.7}
          >
            <View style={styles.tabContent}>
              <Winicon  src={tab.icon} size={20} color={activeTab === tab.id ? ColorThemes.light.primary_main_color : ColorThemes.light.neutral_text_title_color} />
              <Text
                style={{...styles.tabText, color: activeTab === tab.id ? ColorThemes.light.primary_main_color : ColorThemes.light.neutral_text_title_color, fontWeight: activeTab === tab.id ? 'bold' : 'normal',

                  marginLeft: tab.badge && tab.badge > 0 ? 12 : 0,
                }}
              >
                {tab.title}
              </Text>
              {tab.badge && tab.badge > 0 && (
                <View style={styles.tabBadge}>
                  <Text style={styles.tabBadgeText}>
                    {tab.badge > 99 ? '99+' : tab.badge}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Create group button */}
      <TouchableOpacity
        style={styles.createGroupButton}
        activeOpacity={0.7}
        onPress={() => navigate(RootScreen.CreateGroup)}
      >
        <Text style={styles.createGroupIcon}>+</Text>
        <Text style={styles.createGroupText}>Create group</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      
      {/* Header giống trang Home */}
      <HeaderLogo />
      
      {/* Tab navigation và content */}
      <View style={styles.content}>
        {renderTabBar()}

        {/* Socket connection status */}
        {/* {!isConnected && (
          <View style={styles.connectionStatus}>
            <Text style={styles.connectionText}>
              {connectionStatus === 'disconnected' ? 'Đang kết nối...' : 'Mất kết nối'}
            </Text>
          </View>
        )} */}

        {/* Socket Debug Info - chỉ hiển thị trong development */}
        {/* <SocketDebugInfo visible={__DEV__} /> */}

        <View style={styles.tabContentContainer}>
          {renderTabContent()}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  content: {
    flex: 1,
    backgroundColor: 'white',
  },
  tabContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchBar: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E8E8E8',
  },
  searchPlaceholder: {
    color: '#999',
    fontSize: 14,
  },
  tabBar: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tabButton: {
    alignItems: 'flex-start',
    paddingVertical: 8,
    position: 'relative',
  },
  tabContent: {
    position: 'relative',
    flexDirection: 'row',
    alignContent: 'flex-start',
    alignItems: 'flex-start',
    gap: 4,
    justifyContent: 'flex-start',
    

  },
  tabIcon: {
    fontSize: 16,
    marginBottom: 4,
  },
  tabText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    fontWeight: '400',
  },
  activeTabText: {
    color: ColorThemes.light.primary_main_color,
    fontWeight: '700',
  },
  tabBadge: {
    position: 'absolute',
    top: -8,
    left: 12,
    backgroundColor: ColorThemes.light.error_main_color,
    borderRadius: 20,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  tabBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
    lineHeight: 16,
  },
  createGroupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    // paddingVertical: 12,
    marginBottom: 8,
  },
  createGroupIcon: {
    fontSize: 30,
    backgroundColor: '#E8E8E8',
    borderRadius: 20,
    marginRight: 12,
    textAlign: 'center',
    color: ColorThemes.light.neutral_text_title_color,
    lineHeight: 40,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,

  },
  createGroupText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_color,
    fontWeight: '500',
  },
  tabContentContainer: {
    flex: 1,
  },
  connectionStatus: {
    backgroundColor: ColorThemes.light.warning_color,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 4,
  },
  connectionText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default ChatMainScreen;

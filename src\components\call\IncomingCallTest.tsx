import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView } from 'react-native';
import IncomingCallNotification from './IncomingCallNotification';

// Component để test IncomingCallNotification UI
const IncomingCallTest: React.FC = () => {
  const [showNotification, setShowNotification] = useState(false);

  const handleShowNotification = () => {
    setShowNotification(true);
  };

  const handleHideNotification = () => {
    setShowNotification(false);
  };

  const handleAccept = () => {
    console.log('📞 Call accepted');
    setShowNotification(false);
  };

  const handleReject = () => {
    console.log('📞 Call rejected');
    setShowNotification(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>IncomingCall UI Test</Text>
        
        <TouchableOpacity style={styles.button} onPress={handleShowNotification}>
          <Text style={styles.buttonText}>Show Incoming Call</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={handleHideNotification}>
          <Text style={styles.buttonText}>Hide Notification</Text>
        </TouchableOpacity>

        <View style={styles.info}>
          <Text style={styles.infoText}>Status: {showNotification ? 'Showing' : 'Hidden'}</Text>
        </View>
      </View>

      {/* Test Notification */}
      <IncomingCallNotification
        visible={showNotification}
        callerName="John Doe"
        callerAvatar={undefined}
        onAccept={handleAccept}
        onReject={handleReject}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 40,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 20,
    minWidth: 200,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  info: {
    marginTop: 40,
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    alignItems: 'center',
  },
  infoText: {
    fontSize: 16,
    color: '#333',
  },
});

export default IncomingCallTest;

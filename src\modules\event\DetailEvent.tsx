import React, {useEffect, useState} from 'react';
import {
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
  ImageBackground,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {AppSvg} from 'wini-mobile-components';
import {useSelector} from 'react-redux';

import iconSvg from '../../svg/icon';
import {ColorThemes} from '../../assets/skin/colors';
import ActionBar from '../news/DetailNewsComponent/ActionBar';
import Content from '../news/DetailNewsComponent/Content';
import ListHastTag from '../news/DetailNewsComponent/ListHastTag';
import ListComment from '../news/DetailNewsComponent/ListComment';
import BasicInfoEvent from './DetailEventComponent/BasicInfoEvent';
import {newsEventAction} from '../../redux/actions/newEventAction';
import {RootState} from '../../redux/store/store';
import {useNewsEventHook} from '../../redux/reducers/NewsEventReducer';
import {CountdownTimer} from '../news/components/TabEventComponents/components/CountdownTimer';
import {checkTimeStatus} from '../../utils/timeUltis';
import {checkTimeWithNow} from '../../utils/Utils';
import {navigate, RootScreen} from '../../router/router';

const hashtags = 'Chaivino,Thiennguyen,CongDong,YeuThuong';
const defaultImage = require('../../assets/images/default_img.png');

const DetailEvent = () => {
  const route = useRoute();
  const {id} = route.params as {id: string};
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<'started' | 'onGoing' | 'ended'>(
    'started',
  );

  // Sử dụng Redux store thay vì useState
  const item = useSelector((state: RootState) => state.newsEvent.item);
  const {action} = useNewsEventHook();

  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
    if (item) {
      const status = checkTimeStatus(item.DateStart, item.DateEnd);
      setStatus(status || 'started');
    }
  }, [item]);

  const initData = async () => {
    try {
      setLoading(true);
      const data = await newsEventAction.fetchById(id);
      if (data) {
        // Cập nhật item trong Redux store
        action.setData({
          stateName: 'item',
          data: data,
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const navigateToHashtagScreen = (hashtag: string) => {
    navigate(RootScreen.ListByHashtagScreen, {
      hashtag,
      type: 'event',
    });
  };

  if (!item) return null;

  const renderEventImage = () => {
    const imageSource = item.Img ? {uri: item.Img} : defaultImage;

    if (checkTimeWithNow(item.DateStart)) {
      return (
        <ImageBackground source={imageSource} style={styles.cardImage}>
          <CountdownTimer TargetDate={item.DateStart} textSize="large" />
        </ImageBackground>
      );
    }

    return <Image source={imageSource} style={styles.cardImage} />;
  };

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Button back luôn hiển thị ở góc */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}>
        <AppSvg SvgSrc={iconSvg.arrowLeft} size={20} />
      </TouchableOpacity>

      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        {/* Main Image */}
        <View>{renderEventImage()}</View>

        <View style={{paddingHorizontal: 16}}>
          {/* các thao tác */}
          {item && <ActionBar data={item} />}

          {/* thông tin cơ bản */}
          {item && <BasicInfoEvent event={item} />}

          {/* nội dung */}
          {item && <Content data={item?.Content} />}

          {/* hashtag */}
          {item && (
            <ListHastTag
              hashtags={item?.Hashtag || ''}
              onPress={navigateToHashtagScreen}
            />
          )}

          {/* bình luận */}
          {item && <ListComment />}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
  mainImage: {
    width: '100%',
    height: 400,
    resizeMode: 'cover',
  },
  backButton: {
    position: 'absolute',
    top: 40,
    left: 16,
    backgroundColor: ColorThemes.light.secondary1_sub_color,
    width: 35,
    height: 35,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    elevation: 5, // cho Android
  },
  cardImage: {
    width: '100%',
    height: 400,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderRadius: 8,
  },
});

export default DetailEvent;
